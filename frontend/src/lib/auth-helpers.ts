import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { AuthUser, UserRole, isValidUserRole } from '@/lib/types/auth';
import { parseJwtPayload, JwtPayload } from './supabase/client';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { SecurityEventDetails, logSecurityEvent } from '@/lib/security/forensics';
import { getUnifiedSession } from './auth/getUnifiedSession';

// Re-export the main auth functions from supabase/api-helpers
export { withAuth, withServiceRole } from './supabase/api-helpers';
export type { AuthRouteHandler } from './supabase/api-helpers';

// Note: MFA and WebAuthn functions removed - routes are obsolete
// Use Supabase official MFA API instead

// Re-export types needed externally
export type { AuthUser };
export { UserRole };

// Define the expected handler signature including the context parameter
export type RouteHandlerWithAuth = (
  req: NextRequest,
  user: AuthUser,
  supabase: TypedSupabaseClient,
  context: { params?: any }
) => Promise<NextResponse | Response>;

/**
 * Utility function to create a Supabase client for server-side operations
 * using the service role key. Use with caution.
 */
export const createServiceClient = (): SupabaseClient<Database> => {
  // Ensure environment variables are loaded and available
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY!;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Supabase URL or Service Key is missing in environment variables.');
  }

  // For the server service client, we don't need cookie storage
  return createServerClient<Database>(supabaseUrl, supabaseServiceKey, {
    cookies: {
      get: (name: string) => undefined, // Service role doesn't use cookies
      set: (name: string, value: string, options: CookieOptions) => {}, // No-op
      remove: (name: string, options: CookieOptions) => {}, // No-op
    },
  });
};

/**
 * Creates a server client specifically for authenticated user operations.
 * This uses the normal anon key but is meant for authenticated routes.
 */
export const createServerClientForUser = (): SupabaseClient<Database> => {
  // Ensure environment variables are loaded and available
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Supabase URL or Anon Key is missing in environment variables.');
  }

  const cookieStore = cookies();

  return createServerClient<Database>(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        // @ts-expect-error - We know this is a ReadonlyRequestCookies
        const cookie = cookieStore.get?.(name);
        return cookie?.value;
      },
      set(name: string, value: string, options: CookieOptions) {
        try {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          cookieStore.set?.({ name, value, ...options });
        } catch (error) {
          console.error('Error setting cookie:', error);
        }
      },
      remove(name: string, options: CookieOptions) {
        try {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          cookieStore.set?.({ name, value: '', ...options });
        } catch (error) {
          console.error('Error removing cookie:', error);
        }
      },
    },
  });
};

// ============================================================================
// UNIFIED SESSION AUTH FUNCTIONS
// ============================================================================
// These functions support both legacy auth and unified session patterns

/**
 * Check if a user is authenticated using unified session
 *
 * @param cookieStore Optional cookies for server-side usage
 * @returns True if the user is authenticated
 */
export async function isAuthenticated(cookieStore?: ReturnType<typeof cookies>): Promise<boolean> {
  const session = await getUnifiedSession(cookieStore);
  return !!session && !!session.user?.id;
}

/**
 * Legacy isAuthenticated function for backward compatibility
 *
 * @param user The user object from Supabase
 * @returns True if the user is authenticated
 */
export function isAuthenticatedLegacy(user: AuthUser | null): boolean {
  return !!user && !!user.id;
}

/**
 * Check if a user has one of the specified roles using unified session
 *
 * @param cookieStore Optional cookies for server-side usage
 * @param roles The roles to check against
 * @returns True if the user has one of the roles
 */
export async function hasRole(cookieStore: ReturnType<typeof cookies>, roles: UserRole[]): Promise<boolean> {
  const session = await getUnifiedSession(cookieStore);
  if (!session) return false;

  // Check primary role property
  if (session.role && roles.includes(session.role as UserRole)) {
    return true;
  }

  // Check app_metadata.roles array if it exists
  if (session.user.app_metadata?.roles && Array.isArray(session.user.app_metadata.roles)) {
    return session.user.app_metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  return false;
}

/**
 * Legacy hasRole function for backward compatibility
 *
 * @param user The user object from Supabase or AuthUser
 * @param roles The roles to check against
 * @returns True if the user has one of the roles
 */
export function hasRoleLegacy(user: AuthUser | null, roles: UserRole[]): boolean {
  if (!user) return false;

  // Check primary role property
  if (user.role && roles.includes(user.role as UserRole)) {
    return true;
  }

  // Check metadata.roles array if it exists
  if (user.metadata?.roles && Array.isArray(user.metadata.roles)) {
    return user.metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  // Check app_metadata.roles array if it exists
  if (user.app_metadata?.roles && Array.isArray(user.app_metadata.roles)) {
    return user.app_metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  return false;
}

/**
 * Create a Supabase client for API routes (unified session compatible)
 *
 * @returns A Supabase client
 */
export async function createClient() {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async get(name: string): Promise<string> {
          const cookieStore = await cookies();
          const cookie = await cookieStore.get(name);
          return cookie?.value || '';
        },
        async set(name: string, value: string, options: Record<string, unknown>): Promise<void> {
          try {
            const cookieStore = await cookies();
            await cookieStore.set(name, value, options);
          } catch (error) {
            console.error('Error setting cookie:', error);
          }
        },
        async remove(name: string): Promise<void> {
          try {
            const cookieStore = await cookies();
            await cookieStore.delete(name);
          } catch (error) {
            console.error('Error deleting cookie:', error);
          }
        }
      }
    }
  );
}
