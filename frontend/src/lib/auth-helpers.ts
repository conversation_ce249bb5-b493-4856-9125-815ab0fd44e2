import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { parseJwtPayload, JwtPayload } from './supabase/client';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { hasRole } from '@/lib/auth';
import { SecurityEventDetails, logSecurityEvent } from '@/lib/security/forensics';

// Re-export the main auth functions from supabase/api-helpers
export { withAuth, withServiceRole } from './supabase/api-helpers';
export type { AuthRouteHandler } from './supabase/api-helpers';

// Note: MFA and WebAuthn functions removed - routes are obsolete
// Use Supabase official MFA API instead

// Re-export types needed externally
export type { AuthUser } from '@/lib/auth';
export { UserRole } from '@/lib/auth';

// Define the expected handler signature including the context parameter
export type RouteHandlerWithAuth = (
  req: NextRequest,
  user: AuthUser,
  supabase: TypedSupabaseClient,
  context: { params?: any }
) => Promise<NextResponse | Response>;

/**
 * Utility function to create a Supabase client for server-side operations
 * using the service role key. Use with caution.
 */
export const createServiceClient = (): SupabaseClient<Database> => {
  // Ensure environment variables are loaded and available
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY!;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Supabase URL or Service Key is missing in environment variables.');
  }

  // For the server service client, we don't need cookie storage
  return createServerClient<Database>(supabaseUrl, supabaseServiceKey, {
    cookies: {
      get: (name: string) => undefined, // Service role doesn't use cookies
      set: (name: string, value: string, options: CookieOptions) => {}, // No-op
      remove: (name: string, options: CookieOptions) => {}, // No-op
    },
  });
};

/**
 * Creates a server client specifically for authenticated user operations.
 * This uses the normal anon key but is meant for authenticated routes.
 */
export const createServerClientForUser = (): SupabaseClient<Database> => {
  // Ensure environment variables are loaded and available
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Supabase URL or Anon Key is missing in environment variables.');
  }

  const cookieStore = cookies();

  return createServerClient<Database>(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        // @ts-expect-error - We know this is a ReadonlyRequestCookies
        const cookie = cookieStore.get?.(name);
        return cookie?.value;
      },
      set(name: string, value: string, options: CookieOptions) {
        try {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          cookieStore.set?.({ name, value, ...options });
        } catch (error) {
          console.error('Error setting cookie:', error);
        }
      },
      remove(name: string, options: CookieOptions) {
        try {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          cookieStore.set?.({ name, value: '', ...options });
        } catch (error) {
          console.error('Error removing cookie:', error);
        }
      },
    },
  });
};
