import { NextRequest, NextResponse } from 'next/server';
import { withA<PERSON>, AuthUser } from '@/lib/auth-helpers';
import { RateLimitService } from '@/lib/services/rate-limit-service';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

// Define the expected handler signature explicitly
type AuthenticatedHandler = (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
) => Promise<Response>;

// Use the explicit type for the handler parameter
export const withRateLimit = (handler: AuthenticatedHandler) => {
  return withAuth(async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ) => {
    const rateLimitService = new RateLimitService(supabase);

    // Extract file size from request
    let fileSize = 0;
    try {
      // For multipart form data
      if (req.headers.get('content-type')?.startsWith('multipart/form-data')) {
        const formData = await req.formData();
        const file = formData.get('file') as File;
        fileSize = file ? file.size : 0;

        // Clone the formData to pass to the handler
        const clonedFormData = new FormData();
        for (const [key, value] of formData.entries()) {
          clonedFormData.append(key, value);
        }

        // Store the cloned formData in the request context for the handler
        req.formData = () => Promise.resolve(clonedFormData);
      } else {
        // For JSON requests with file info
        const body = await req.json();
        fileSize = body.fileSize || 0;

        // Store the parsed body in the request context for the handler
        req.json = () => Promise.resolve(body);
      }
    } catch (error) {
      console.error('Error extracting file size:', error);
    }

    // Check rate limit
    if (!user.tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const { allowed, reason } = await rateLimitService.canUploadDocument(
      user.tenantId,
      fileSize
    );

    if (!allowed) {
      return NextResponse.json(
        { error: `Rate limit exceeded: ${reason}` },
        { status: 429 }
      );
    }

    // Track usage if this is a POST/PUT request (document upload)
    if (['POST', 'PUT'].includes(req.method || '') && user.tenantId) {
      await rateLimitService.trackDocumentUpload(user.tenantId, fileSize);
    }

    // Continue to handler
    return handler(req, user, supabase, context);
  });
};
