import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { AuthUser, UserRole, isValidUserRole } from '@/lib/types/auth';
import { parseJwtPayload, JwtPayload } from '../supabase/client';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { getUnifiedSession } from './getUnifiedSession';

/**
 * Check if a user is authenticated using unified session
 *
 * @param cookieStore Optional cookies for server-side usage
 * @returns True if the user is authenticated
 */
export async function isAuthenticated(cookieStore?: ReturnType<typeof cookies>): Promise<boolean> {
  const session = await getUnifiedSession(cookieStore);
  return !!session && !!session.user?.id;
}

/**
 * Legacy isAuthenticated function for backward compatibility
 *
 * @param user The user object from Supabase
 * @returns True if the user is authenticated
 */
export function isAuthenticatedLegacy(user: AuthUser | null): boolean {
  return !!user && !!user.id;
}

/**
 * Check if a user has one of the specified roles using unified session
 *
 * @param cookieStore Optional cookies for server-side usage
 * @param roles The roles to check against
 * @returns True if the user has one of the roles
 */
export async function hasRole(cookieStore: ReturnType<typeof cookies>, roles: UserRole[]): Promise<boolean> {
  const session = await getUnifiedSession(cookieStore);
  if (!session) return false;

  // Check primary role property
  if (session.role && roles.includes(session.role as UserRole)) {
    return true;
  }

  // Check app_metadata.roles array if it exists
  if (session.user.app_metadata?.roles && Array.isArray(session.user.app_metadata.roles)) {
    return session.user.app_metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  return false;
}

/**
 * Legacy hasRole function for backward compatibility
 *
 * @param user The user object from Supabase or AuthUser
 * @param roles The roles to check against
 * @returns True if the user has one of the roles
 */
export function hasRoleLegacy(user: AuthUser | null, roles: UserRole[]): boolean {
  if (!user) return false;

  // Check primary role property
  if (user.role && roles.includes(user.role as UserRole)) {
    return true;
  }

  // Check metadata.roles array if it exists
  if (user.metadata?.roles && Array.isArray(user.metadata.roles)) {
    return user.metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  // Check app_metadata.roles array if it exists
  if (user.app_metadata?.roles && Array.isArray(user.app_metadata.roles)) {
    return user.app_metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  return false;
}

/**
 * Create a Supabase client for API routes (unified session compatible)
 *
 * @returns A Supabase client
 */
export async function createClient() {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async get(name: string): Promise<string> {
          const cookieStore = await cookies();
          const cookie = await cookieStore.get(name);
          return cookie?.value || '';
        },
        async set(name: string, value: string, options: Record<string, unknown>): Promise<void> {
          try {
            const cookieStore = await cookies();
            await cookieStore.set(name, value, options);
          } catch (error) {
            console.error('Error setting cookie:', error);
          }
        },
        async remove(name: string): Promise<void> {
          try {
            const cookieStore = await cookies();
            await cookieStore.delete(name);
          } catch (error) {
            console.error('Error deleting cookie:', error);
          }
        }
      }
    }
  );
}

/**
 * Create a service client for server-to-server communication
 *
 * @returns A Supabase client with service role key
 */
export function createServiceClient(): TypedSupabaseClient {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get() { return ''; },
        set() { },
        remove() { }
      }
    }
  );
}
