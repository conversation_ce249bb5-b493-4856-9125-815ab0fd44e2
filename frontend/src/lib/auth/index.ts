/**
 * Authentication Module - Main Entry Point
 *
 * This is the single entry point for all authentication functionality.
 * Import everything you need from '@/lib/auth'
 */

// === CORE MODULES ===
// Client creation and management
export * from './client';

// Session management and route protection
export * from './session';

// Permissions and role checking
export * from './permissions';

// JWT utilities and verification
export * from './jwt';

// Type definitions
export * from './types';

// === LEGACY COMPONENTS (keep for now) ===
// React components and hooks
export { AuthProvider } from './AuthProvider';
export { AuthContext, useAuth } from './useAuth';
export { useRequireAuth } from './useRequireAuth';
export { useAuthToken } from './useAuthToken';
export { useRequireRole } from './useRequireRole';

// === MAIN EXPORTS FOR COMMON USE ===
// Re-export the most commonly used functions for convenience

// Client creation
export {
  createClient,
  createServiceClient,
  createBrowserSupabaseClient,
  createServerClientForUser,
  supabaseClient
} from './client';

// Session management
export {
  withAuth,
  withServiceRole,
  getServerSession,
  getUser,
  requireAuth,
  createAuthUserFromSession
} from './session';

// Permissions
export {
  hasRole,
  isSuperAdmin,
  isAuthenticated,
  checkPermission,
  getUserPermissions,
  canAccessTenant,
  hasRoleLegacy,
  isAuthenticatedLegacy
} from './permissions';

// JWT utilities
export {
  verifyJwt,
  verifyJWT,
  debugJwtClaims,
  parseClaims,
  extractTenantId,
  extractRole,
  isTokenExpired,
  getTokenTimeRemaining
} from './jwt';

// Types
export {
  UserRole,
  SUPER_ADMIN_EMAILS,
  isValidUserRole,
  type AuthUser,
  type TenantClaims,
  type AuthRouteHandler,
  type AuthResult,
  type SessionExt,
  type SuperAdminEmail
} from './types';
