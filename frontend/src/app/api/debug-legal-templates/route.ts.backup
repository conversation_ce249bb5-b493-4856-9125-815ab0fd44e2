// @ts-nocheck - API route type issues
// src/app/api/debug-legal-templates/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Using withAuth with admin role restriction to ensure this diagnostic endpoint is only accessible to admins
// and has the appropriate service role access
// This is for app managers with admin access
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  // Check if user has admin role
  if (user.role !== 'admin' && user.role !== 'superadmin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }
  try {
    console.log('Debug route: Starting comprehensive test with SQL queries');
    console.log('User running diagnostics:', {
      id: user.id,
      email: user.email,
      role: user.role,
    });

    // Log environment variables (partial for security)
    console.log('Connection parameters:', {
      url: process.env.NEXT_PUBLIC_SUPABASE_URL,
      anon_key_prefix: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 5) + '...',
      service_key_prefix: process.env.SUPABASE_SERVICE_KEY?.substring(0, 5) + '...'
    });

    // Note: Using the supabase client passed from withAdminAuth - no need to create a new one

    // Test 1: Raw SQL query to check schema permissions
    const { data: schemaData, error: schemaError } = await supabase.rpc(
      'check_schema_permissions',
      { schema_name: 'public' }
    );

    // Test 2: Direct SQL query for legal_templates
    const { data: sqlLegalTemplates, error: sqlLegalTemplatesError } = await supabase
      .rpc('execute_sql', {
        sql_query: 'SELECT * FROM public.legal_templates LIMIT 1'
      });

    // Test 3: Direct SQL query for tasks (known working table)
    const { data: sqlTasks, error: sqlTasksError } = await supabase
      .rpc('execute_sql', {
        sql_query: 'SELECT * FROM tenants.tasks LIMIT 1'
      });

    // Test 4: Standard API query with schema
    // Using any type to bypass TypeScript checking for schema method
    const { data: apiLegalTemplates, error: apiLegalTemplatesError } = await (supabase as any)
      .schema('public')
      .from('legal_templates')
      .select('*')
      .limit(1);

    // Test 5: Direct REST API call
    const apiUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/legal_templates?limit=1`;
    const apiKey = process.env.SUPABASE_SERVICE_KEY || '';

    const directApiResponse = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'apikey': apiKey,
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    const directApiStatus = directApiResponse.status;
    const directApiStatusText = directApiResponse.statusText;
    const directApiBody = directApiResponse.ok ? await directApiResponse.json() : null;

    // Test 6: Check if the table exists
    const { data: tableExists, error: tableExistsError } = await supabase
      .rpc('execute_sql', {
        sql_query: "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'legal_templates')"
      });

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId
      },
      schemaPermissions: {
        data: schemaData,
        error: schemaError
      },
      sqlLegalTemplates: {
        data: sqlLegalTemplates,
        error: sqlLegalTemplatesError
      },
      sqlTasks: {
        data: sqlTasks,
        error: sqlTasksError
      },
      apiLegalTemplates: {
        data: apiLegalTemplates,
        error: apiLegalTemplatesError
      },
      directApi: {
        status: directApiStatus,
        statusText: directApiStatusText,
        body: directApiBody
      },
      tableExists: {
        data: tableExists,
        error: tableExistsError
      }
    });
  } catch (error: unknown) {
    console.error('Error debugging legal templates:', error);
    let errorMessage = 'Failed to debug legal templates';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json(
      { error: 'Debug operation failed', details: errorMessage },
      { status: 500 }
    );
  }
}, ['partner', 'admin']); // Only partner or admin can debug templates
