// frontend/src/app/api/deadlines/validation/route.ts
import { withAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth-helpers';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { z } from 'zod';

// GET /api/deadlines/validation - Get all pending deadlines for validation
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Ensure tenantId exists before proceeding
    if (!user.tenantId) {
      console.error('User does not have a tenantId');
      return NextResponse.json({ error: 'User tenant association missing.' }, { status: 400 });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const page = Number(url.searchParams.get('page') ?? 1);
    const limit = Number(url.searchParams.get('limit') ?? 50);

    // Query for pending validation deadlines
    const { data: pendingDeadlines, error, count } = await supabase
      .schema('tenants')
      .from('deadlines')
      .select('*, documents(title), cases(title)', { count: 'exact' })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'pending')
      .order('due_date', { ascending: true })
      .range((page - 1) * limit, page * limit - 1);

    if (error) {
      console.error('Error fetching pending deadlines:', error);
      return NextResponse.json({ error: 'Failed to fetch pending deadlines' }, { status: 500 });
    }

    // Get validation statistics
    // Using 'as any' because Supabase types don't fully support .group() after .select()
    const { data: stats, error: statsError } = await (supabase as any)
      .schema('tenants')
      .from('deadlines')
      .select('validation_status, count(*)')
      .eq('tenant_id', user.tenantId)
      .group('validation_status');

    if (statsError) {
      console.error('Error fetching validation stats:', statsError);
      return NextResponse.json({
        pendingDeadlines,
        pagination: {
          page,
          limit,
          totalCount: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      });
    }

    // Define interface for stats items
    interface ValidationStatsItem {
      validation_status: 'pending' | 'validated' | 'rejected' | null;
      count: number | string; // count() might return string
    }

    // Process statistics
    const validationStats = {
      counts: {
        pending: 0,
        validated: 0,
        rejected: 0
      },
      percentages: {
        pending: 0,
        validated: 0,
        rejected: 0
      },
      total: 0
    };

    // Calculate counts
    (stats as ValidationStatsItem[]).forEach((stat: ValidationStatsItem) => {
      const status = stat.validation_status || 'pending'; // Default to pending if null
      // Ensure count is treated as a number
      const count = typeof stat.count === 'string' ? parseInt(stat.count, 10) : stat.count;

      if (isNaN(count)) {
        console.warn('Skipping stat item with invalid count:', stat);
        return; // Skip this item if count is not a valid number
      }

      if (status === 'pending') validationStats.counts.pending = count;
      else if (status === 'validated') validationStats.counts.validated = count;
      else if (status === 'rejected') validationStats.counts.rejected = count;

      validationStats.total += count;
    });

    // Calculate percentages
    if (validationStats.total > 0) {
      validationStats.percentages.pending = Math.round((validationStats.counts.pending / validationStats.total) * 100);
      validationStats.percentages.validated = Math.round((validationStats.counts.validated / validationStats.total) * 100);
      validationStats.percentages.rejected = Math.round((validationStats.counts.rejected / validationStats.total) * 100);
    }

    return NextResponse.json({
      pendingDeadlines,
      stats: validationStats,
      pagination: {
        page,
        limit,
        totalCount: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/deadlines/validation:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
