import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import { UserRole } from '@/lib/types/auth';
import { graphService } from '@/lib/services/graph-service';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

// Define the GraphData interface
interface GraphData {
  nodes: Array<{
    id: string;
    name: string;
    type: string;
    [key: string]: unknown;
  }>;
  links: Array<{
    source: string;
    target: string;
    type: string;
    [key: string]: unknown;
  }>;
}

// Define roles who can access this endpoint
const ALLOWED_ROLES: UserRole[] = [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff];

export const GET = withAuth(
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    // Extract params from context safely
    const params = context.params;
    if (typeof params !== 'object' || params === null || !('personId' in params) || typeof params.personId !== 'string') {
      return NextResponse.json({ error: 'Invalid person ID in URL' }, { status: 400 });
    }
    const personId = params.personId;

    const tenantId = user.tenantId;
    if (!tenantId) {
        return NextResponse.json({ error: 'Tenant ID not found in user session' }, { status: 400 });
    }

    try {
      console.log(`API Route: Fetching network for person ${personId} in tenant ${tenantId}`);
      const graphData = await graphService.getPersonNetwork(personId, tenantId);
      console.log(`API Route: Found ${graphData.nodes.length} nodes and ${graphData.links.length} links for person ${personId}`);
      return NextResponse.json(graphData);
    } catch (error: unknown) {
      console.error(`API Error fetching person network for person ${personId}:`, error);
      return NextResponse.json({ error: 'Failed to retrieve person network.' }, { status: 500 });
    }
  }
);
