import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import { UserRole } from '@/lib/types/auth';
import { graphService } from '@/lib/services/graph-service';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

// Define the Case interface
interface Case {
  id: string;
  title: string;
  status: string;
  created_at: string;
  [key: string]: unknown;
}

// Define staff roles who can access this endpoint
const ALLOWED_ROLES: UserRole[] = [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff];

export const GET = withAuth(
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    // Extract params from context safely
    const params = context.params;
    if (typeof params !== 'object' || params === null || !('staffId' in params) || typeof params.staffId !== 'string') {
      return NextResponse.json({ error: 'Invalid staff ID in URL' }, { status: 400 });
    }
    const staffId = params.staffId;

    const tenantId = user.tenantId;

    if (!tenantId) {
        return NextResponse.json({ error: 'Tenant ID not found in user session' }, { status: 400 });
    }

    // Optional: Add explicit role check if needed beyond withAuth's checks
    // if (!ALLOWED_ROLES.includes(user.role)) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    // }

    try {
      console.log(`API Route: Fetching caseload for staff ${staffId} in tenant ${tenantId}`);
      const cases = await graphService.getStaffCaseLoad(staffId, tenantId);
      console.log(`API Route: Found ${cases.length} cases for staff ${staffId}`);
      return NextResponse.json(cases);
    } catch (error: unknown) {
      console.error(`API Error fetching staff caseload for staff ${staffId}:`, error);
      // Use a generic error message for the client
      return NextResponse.json({ error: 'Failed to retrieve staff caseload.' }, { status: 500 });
    }
  }
);
