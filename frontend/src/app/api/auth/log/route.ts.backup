// @ts-nocheck - API route type issues
/**
 * Authentication Logging API
 *
 * Handles logging of authentication events:
 * - Lo<PERSON> attempts
 * - Login successes
 * - Login failures
 * - Logout events
 * - Token refreshes
 * - Suspicious activities
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAuth, withServiceRole } from '@/lib/auth-helpers';
import { logSecurityEvent } from '@/lib/security/forensics';
import {
  logSuccessfulAuth,
  logFailedAuth,
  logTokenRefresh
} from '@/lib/security/auth-security-logger';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

/**
 * Public endpoint for logging authentication events
 * Some events (like login attempts) need to be logged before authentication
 */
export async function POST(req: NextRequest) {
  try {
    const { event, email, userId, reason, deviceFingerprint, tokenId } = await req.json();

    // Get IP address from request
    const ipAddress = req.headers.get('x-forwarded-for') ||
                      req.headers.get('x-real-ip') ||
                      'unknown';

    // Handle different event types
    switch (event) {
      case 'auth.login_attempt':
        // Log login attempt (before authentication)
        await logSecurityEvent('auth.login_attempt', {
          email,
          deviceFingerprint,
          ipAddress,
          timestamp: new Date().toISOString()
        });
        break;

      case 'auth.login_success':
        // Log successful login
        if (!userId) {
          return NextResponse.json(
            { error: 'Missing userId parameter' },
            { status: 400 }
          );
        }

        await logSuccessfulAuth(
          userId,
          email || 'unknown',
          deviceFingerprint,
          ipAddress as string
        );
        break;

      case 'auth.login_failed':
        // Log failed login
        if (!email) {
          return NextResponse.json(
            { error: 'Missing email parameter' },
            { status: 400 }
          );
        }

        await logFailedAuth(
          email,
          reason || 'Unknown reason',
          deviceFingerprint,
          ipAddress as string
        );
        break;

      case 'auth.logout':
        // Log logout event
        await logSecurityEvent('auth.logout', {
          userId,
          email,
          deviceFingerprint,
          ipAddress,
          timestamp: new Date().toISOString()
        });
        break;

      case 'auth.token_refresh':
        // Log token refresh
        if (!userId) {
          return NextResponse.json(
            { error: 'Missing userId parameter' },
            { status: 400 }
          );
        }

        await logTokenRefresh(
          userId,
          deviceFingerprint,
          tokenId
        );
        break;

      case 'auth.suspicious_activity':
        // Log suspicious activity
        await logSecurityEvent('auth.suspicious_activity', {
          userId,
          email,
          deviceFingerprint,
          ipAddress,
          reason,
          severity: 'high',
          timestamp: new Date().toISOString()
        });
        break;

      default:
        return NextResponse.json(
          { error: `Unknown event type: ${event}` },
          { status: 400 }
        );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Auth logging error:', error);
    return NextResponse.json(
      { error: 'Failed to log authentication event' },
      { status: 500 }
    );
  }
}

/**
 * Protected endpoint for retrieving authentication logs
 * Only accessible to admin users
 */
export const GET = withAuth(
  async (req, user, supabase) => {
    try {
      // Only allow partners and attorneys to view auth logs
      if (!['partner', 'attorney'].includes(user.role)) {
        return NextResponse.json(
          { error: 'Insufficient permissions to view auth logs' },
          { status: 403 }
        );
      }

      const { searchParams } = new URL(req.url);
      const userId = searchParams.get('userId');
      const eventType = searchParams.get('eventType');
      const limit = parseInt(searchParams.get('limit') || '50');

      // Enhance the client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Build query
      let query = enhancedClient.security
        .from('auth_audit')
        .select('*')
        .order('event_time', { ascending: false })
        .limit(limit);

      // Add filters if provided
      if (userId) {
        query = query.eq('user_id', userId);
      }

      if (eventType) {
        query = query.eq('operation', eventType);
      }

      // Execute query
      const { data, error } = await query;

      if (error) {
        console.error('Error fetching auth logs:', error);
        return NextResponse.json(
          { error: 'Failed to fetch authentication logs' },
          { status: 500 }
        );
      }

      return NextResponse.json({ logs: data });
    } catch (error) {
      console.error('Auth log retrieval error:', error);
      return NextResponse.json(
        { error: 'Failed to retrieve authentication logs' },
        { status: 500 }
      );
    }
  },
  // Only partners and attorneys can view auth logs
  ['partner', 'attorney']
);
