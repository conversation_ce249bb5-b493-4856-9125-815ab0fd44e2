// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server'
import { SupabaseClient } from '@supabase/supabase-js'
import { withAuth, AuthUser } from '@/lib/auth-helpers'

/**
 * GET /api/cases/statistics
 *
 * Retrieves case statistics for the dashboard
 */
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient
) => {
  try {
    // Validate tenantId is defined
    if (!user.tenantId) {
      console.error('tenantId is undefined in JWT claims');
      return NextResponse.json(
        { error: 'Authorization error: Missing tenant information' },
        { status: 400 }
      );
    }

    // First, fetch total and active case counts
    const { data: casesData, error: casesError } = await supabase
      .schema('tenants')
      .from('cases')
      .select('id, status')
      .eq('tenant_id', user.tenantId)

    if (casesError) {
      console.error('Error fetching case counts:', casesError)
      return NextResponse.json(
        { error: 'Failed to fetch case statistics' },
        { status: 500 }
      )
    }

    // Calculate basic case stats
    const totalCases = casesData.length
    const activeCases = casesData.filter(c => c.status === 'active').length

    // Get case IDs for subsequent queries
    const caseIds = casesData.map(c => c.id)

    // If no cases, return early with zeros
    if (caseIds.length === 0) {
      return NextResponse.json({
        totalCases: 0,
        activeCases: 0,
        upcomingDeadlines: 0,
        documentCount: 0
      })
    }

    // Fetch upcoming deadlines count (deadlines in the future, not completed)
    const now = new Date().toISOString()

    // Only query if we have caseIds
    let upcomingDeadlines = 0;
    let deadlinesError = null;

    if (caseIds.length > 0) {
      const result = await supabase
        .schema('tenants')
        .from('deadlines')
        .select('id', { count: 'exact', head: true })
        .eq('tenant_id', user.tenantId)
        .in('case_id', caseIds)
        .gt('due_date', now)
        .eq('completed', false);

      upcomingDeadlines = result.count || 0;
      deadlinesError = result.error;
    }

    if (deadlinesError) {
      console.error('Error fetching deadline count:', deadlinesError)
      // Continue with other stats
    }

    // Fetch document count associated with all cases
    let documentCount = 0;
    let documentsError = null;

    if (caseIds.length > 0) {
      const result = await supabase
        .schema('tenants')
        .from('document_cases')
        .select('document_id', { count: 'exact', head: true })
        .eq('tenant_id', user.tenantId)
        .in('case_id', caseIds);

      documentCount = result.count || 0;
      documentsError = result.error;
    }

    if (documentsError) {
      console.error('Error fetching document count:', documentsError)
      // Continue with other stats
    }

    // Return all statistics
    return NextResponse.json({
      totalCases,
      activeCases,
      upcomingDeadlines: upcomingDeadlines || 0,
      documentCount: documentCount || 0
    })
  } catch (error) {
    console.error('Error in case statistics endpoint:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
})
