// frontend/src/app/api/cases/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withA<PERSON>, AuthUser } from '@/lib/auth-helpers';
import { z } from 'zod';
import type { Database } from '@/lib/supabase/database.types';
import { createDataAccess } from '@/lib/data-access';
import { createServices } from '@/lib/services';

// Define interfaces for case data

interface Case {
  id: string;
  title: string;
  description?: string | null;
  sensitive?: boolean | null; // Allow null for sensitive
  client_id: string | null;  // Allow null for client_id
  metadata?: any;  // Allow any type for metadata including Json
  status?: string;  // Use string instead of CaseStatus enum
  rejection_reason?: string | null;
  created_at?: string | null; // Allow null
  updated_at?: string | null;
  [key: string]: any;
}

interface CaseQueryParams {
  status?: string;
  client_id?: string;
  searchTerm?: string;
  page: number;
  limit: number;
}

interface CaseQueryResult {
  cases: Case[];
  total: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}

// We'll use the schema from the case service
/*
const CaseSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().nullable().optional(),
  sensitive: z.boolean().optional().default(false),
  client_id: z.string().uuid('Invalid client ID'),
  metadata: z.record(z.any()).optional().default(() => ({})),
  status: z.enum(['pending', 'active', 'closed', 'rejected']).optional().default('pending'),
  rejection_reason: z.string().nullable().optional(),
});
*/

// GET /api/cases - Fetch all cases for the current tenant using the data access layer
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const { searchParams } = new URL(req.url);

    // Extract all query parameters with proper defaults
    const status = searchParams.get('status') || undefined;
    const client_id = searchParams.get('client_id') || undefined;
    const practiceArea = searchParams.get('practiceArea') || undefined;
    const searchTerm = searchParams.get('search') || undefined;

    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);

    // Ensure sensible limits
    const sanitizedPage = Math.max(1, page);
    const sanitizedLimit = Math.min(100, Math.max(1, limit));

    // Log full user object for debugging
    console.log('API User object:', {
      id: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId,
      metadata: user.metadata,
      exp: user.exp,
    });

    // Try to get tenant ID, first from user object, then attempt to fetch from database if needed
    let tenantId = user.tenantId;

    // If tenant ID is missing, try to fetch it directly from the tenants.users table
    if (!tenantId) {
      console.warn('Missing tenant ID in user object, attempting to fetch from database:', user.id);

      try {
        // Use rpc to access tenants schema tables since direct cross-schema access has type issues
        // The get_user_tenant_id function returns a jsonb with tenant_id and role
        // TypeScript error workaround - using as any since the RPC function is newly created
        const { data: tenantData, error: tenantError } = await (supabase as any)
          .rpc('get_user_tenant_id', { user_id: user.id });

        // Log the RPC result for debugging
        console.log('RPC get_user_tenant_id result:', { tenantData, tenantError });

        if (tenantData && typeof tenantData === 'object' && tenantData.tenant_id) {
          console.log('Successfully retrieved tenant ID from database:', tenantData.tenant_id);
          tenantId = tenantData.tenant_id;

          // Update the user object as well to ensure consistency
          user.tenantId = tenantData.tenant_id;
        } else if (tenantError) {
          console.error('Error fetching tenant ID from database:', tenantError);
          throw new Error(`Failed to retrieve tenant ID: ${tenantError.message}`);
        } else {
          console.error('User not found in tenants.users table or has no tenant ID');
          throw new Error('User account not found in tenant database or missing tenant association');
        }
      } catch (fetchError) {
        console.error('Exception while fetching tenant data:', fetchError);
        return NextResponse.json({
          error: 'User is not associated with a tenant',
          details: 'Your user account is not properly linked to a law firm. Please contact support.'
        }, { status: 400 });
      }
    }

    // Log the tenant ID we'll be using (either from user or fetched from DB)
    console.log('Fetching cases for tenant ID:', tenantId);

    // Create a data access layer with the tenant ID (either from user object or from our lookup)
    // Need to cast tenantId as string since we've confirmed it exists by this point
    const dataAccess = createDataAccess(supabase, tenantId as string);

    // Use the cases repository to fetch cases
    const result = await dataAccess.cases.getCases({
      page: sanitizedPage,
      limit: sanitizedLimit,
      status,
      client_id,
      searchTerm
    });

    // Format the response
    const response: CaseQueryResult = {
      cases: result.data || [],
      total: result.count || 0,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages
    };

    // Apply practice area filter if needed (this would normally be in the database query)
    if (practiceArea && practiceArea !== 'all') {
      response.cases = response.cases.filter(c => {
        // Convert practice area to match filter format (e.g. 'Medical Malpractice' to 'medical-malpractice')
        const casePracticeArea = (c.metadata?.practiceArea || '')
          .toLowerCase()
          .replace(/\s+/g, '-');
        return casePracticeArea === practiceArea;
      });
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in GET /api/cases:', error instanceof Error ? error.message : String(error));
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace available');
    return NextResponse.json({ error: 'Internal server error', details: error instanceof Error ? error.message : String(error) }, { status: 500 });
  }
});

// POST /api/cases - Create a new case
export const POST = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const body = await req.json();

    // Ensure tenantId exists for creating cases
    if (!user.tenantId) {
      return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
    }

    // Create a data access layer
    const dataAccess = createDataAccess(supabase, user.tenantId);

    // Create a new case using the repository
    const newCase = await dataAccess.cases.create({
      ...body,
      created_by: user.id
    });

    return NextResponse.json(newCase, { status: 201 });
  } catch (error: any) {
    console.error('Error in POST /api/cases:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
