import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import { UserRole } from '@/lib/types/auth';
import { createServices } from '@/lib/services';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { z } from 'zod';

// Note creation schema
const NoteSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  category: z.string().min(1, 'Category is required'),
  tags: z.string().optional(), // Comma-separated tags
  metadata: z.record(z.any()).optional(), // Add optional metadata field
  case_id: z.string().uuid('Invalid case ID'),
});

// GET /api/cases/[id]/notes - Fetch all notes for a specific case
export const GET = withAuth(
  async (req: NextRequest, user: AuthUser, supabase, context?: Record<string, unknown>) => {
    try {
      // Safely access the case ID from context
      const caseId = typeof context?.params === 'object' && context.params !== null && typeof (context.params as { id: string }).id === 'string'
        ? (context.params as { id: string }).id
        : null;

      if (!caseId) {
        return NextResponse.json({ error: 'Valid Case ID is required in path' }, { status: 400 });
      }

      // Ensure tenantId exists for case-specific operations
      if (!user.tenantId) {
        return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
      }

      // Validate query parameters
      const url = new URL(req.url);
      const { searchParams } = url;
      const searchTerm = searchParams.get('search') || undefined;
      const category = searchParams.get('category') || undefined;

      // Create services instance with validated tenantId
      const services = createServices(supabase as TypedSupabaseClient, user.tenantId);

      try {
        // Get notes for the case
        const notes = await services.notes.getByCaseId(caseId, {
          searchTerm,
          category,
          sortBy: 'created_at',
          sortOrder: 'desc'
        });

        return NextResponse.json({ notes });
      } catch (error) {
        if (error instanceof Error && error.message === 'Case not found') {
          return NextResponse.json({ error: 'Case not found' }, { status: 404 });
        }
        throw error;
      }
    } catch (error: unknown) {
      console.error('Error in GET /api/cases/[id]/notes:', error);
      return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
    }
  }
);

// Define interface for POST request body
type PostNoteRequestBody = z.infer<typeof NoteSchema>;

// POST /api/cases/[id]/notes - Create a new note for a case
export const POST = withAuth(
  async (req: NextRequest, user: AuthUser, supabase, context?: Record<string, unknown>) => {
    try {
      // Safely access the case ID from context
      const caseId = typeof context?.params === 'object' && context.params !== null && typeof (context.params as { id: string }).id === 'string'
        ? (context.params as { id: string }).id
        : null;

      if (!caseId) {
        return NextResponse.json({ error: 'Valid Case ID is required in path' }, { status: 400 });
      }

      // Ensure tenantId exists for case-specific operations
      if (!user.tenantId) {
        return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
      }

      const body: PostNoteRequestBody = await req.json();

      // Add case_id to the body
      body.case_id = caseId;

      // Create services instance with validated tenantId
      const services = createServices(supabase as TypedSupabaseClient, user.tenantId);

      try {
        // Validate note data
        const validatedData = NoteSchema.parse(body);

        // Process tags if present
        if (validatedData.tags) {
          // Convert comma-separated tags to array
          const tagsArray = validatedData.tags
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag.length > 0);

          // Add to metadata
          validatedData.metadata = {
            ...(validatedData.metadata || {}),
            tags: tagsArray
          };
        }

        // Create the note
        const note = await services.notes.create(validatedData, user.id);

        return NextResponse.json(note, { status: 201 });
      } catch (error) {
        if (error instanceof Error && error.message === 'Case not found') {
          return NextResponse.json({ error: 'Case not found' }, { status: 404 });
        } else if (error instanceof z.ZodError) {
          return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
        }
        throw error;
      }
    } catch (error: unknown) {
      console.error('Error in POST /api/cases/[id]/notes:', error);
      if (error instanceof z.ZodError) {
        return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
      }
      return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
    }
  }
);
