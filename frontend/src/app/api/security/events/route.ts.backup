// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { withAuthCallback, createServiceClient } from '@/lib/auth-helpers';
import { Database } from '@/lib/database.types';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

// Define interfaces for security event data
interface SecurityEvent {
  id: string;
  event_type: string;
  event_category: string;
  user_id: string;
  ip_address?: string;
  user_agent?: string;
  location_city?: string;
  location_region?: string;
  location_country?: string;
  details?: Record<string, any>;
  created_at: string;
}

interface TenantUser {
  auth_user_id: string;
}

interface UserData {
  role: string;
  tenant_id: string;
}

/**
 * API endpoint for querying security events
 * This endpoint is protected and should only be accessible to authenticated users with proper permissions
 */
export async function GET(req: NextRequest): Promise<Response> {
  console.log('Security events API called');

  return withAuthCallback(req, async (userId) => {
    try {
      console.log('Security events API authenticated with user:', userId);

      // Create a Supabase client with service role
      const supabase = createServiceClient(req);

      // Enhance the client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Get query parameters
      const searchParams = req.nextUrl.searchParams;
      const category = searchParams.get('category');
      const eventType = searchParams.get('eventType');
      const limit = parseInt(searchParams.get('limit') || '100');
      const requestedUserId = searchParams.get('userId');

      console.log('Security events API parameters:', { category, eventType, limit, requestedUserId });

      // Check if the user exists in tenants.users
      const { data: userData, error: userError } = await enhancedClient.tenants
        .from('users')
        .select('role, tenant_id')
        .eq('auth_user_id', userId)
        .single() as { data: UserData | null, error: any };

      if (userError) {
        console.error('Error fetching user data:', userError);

        // If the user doesn't exist, return mock data for development
        console.log('User not found, returning mock data');
        return NextResponse.json({
          data: createMockEvents(requestedUserId || userId),
          mock: true
        });
      }

      if (!userData) {
        console.log('User data is null, returning mock data');
        return NextResponse.json({
          data: createMockEvents(requestedUserId || userId),
          mock: true
        });
      }

      console.log('User data found:', userData);

      const isAdmin = userData.role === 'admin' || userData.role === 'superadmin' || userData.role === 'partner';

      // If not admin, they can only see their own events
      if (!isAdmin && requestedUserId && requestedUserId !== userId) {
        return NextResponse.json(
          { error: 'Unauthorized access. Cannot view events for other users.' },
          { status: 403 }
        );
      }

      // For tenant admins, they can only see events in their tenant
      let tenantFilter = null;
      if (userData.role !== 'superadmin') {
        // Get all users in the tenant
        const { data: tenantUsers, error: tenantUsersError } = await enhancedClient.tenants
          .from('users')
          .select('auth_user_id')
          .eq('tenant_id', userData.tenant_id) as { data: TenantUser[] | null, error: any };

        if (tenantUsersError) {
          console.error('Error fetching tenant users:', tenantUsersError);
          return NextResponse.json({
            data: createMockEvents(requestedUserId || userId),
            mock: true
          });
        }

        tenantFilter = tenantUsers?.map((user: TenantUser) => user.auth_user_id) || [];
      }

      // Check if the security.events table exists
      try {
        // Try to query the security.events table
        // Create a custom schema for security events
        const customSchema = {
          name: 'events',
          schema: 'security'
        };

        // Using any type to bypass TypeScript checking for tables that might not be in the Database type
        const { error: tableError } = await supabase
          .from(customSchema.name as any)
          .select('id')
          .limit(1);

        if (tableError) {
          console.error('Error checking security.events table:', tableError);
          return NextResponse.json({
            data: createMockEvents(requestedUserId || userId),
            mock: true
          });
        }

        console.log('Security.events table exists');
      } catch (tableCheckError: any) {
        console.error('Error checking security.events table:', tableCheckError);
        return NextResponse.json({
          data: createMockEvents(requestedUserId || userId),
          mock: true
        });
      }

      // Build the query
      // Create a custom schema for security events
      const customSchema = {
        name: 'events',
        schema: 'security'
      };

      // Using any type to bypass TypeScript checking for tables that might not be in the Database type
      let query = supabase
        .from(customSchema.name as any)
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      // Apply filters
      if (requestedUserId) {
        query = query.eq('user_id', requestedUserId);
      } else if (!isAdmin) {
        // Non-admins can only see their own events
        query = query.eq('user_id', userId);
      } else if (tenantFilter && tenantFilter.length > 0) {
        // Tenant admins can see events for all users in their tenant
        query = query.in('user_id', tenantFilter);
      }

      if (category && category !== 'all') {
        query = query.eq('event_category', category);
      }

      if (eventType) {
        query = query.eq('event_type', eventType);
      }

      // Execute the query
      const { data, error } = await query;

      // Cast the result to the expected type
      const typedData = data as SecurityEvent[] | null;

      if (error) {
        console.error('Error fetching security events:', error);
        return NextResponse.json({
          data: createMockEvents(requestedUserId || userId),
          mock: true
        });
      }

      if (!typedData || typedData.length === 0) {
        console.log('No security events found, returning mock data');
        return NextResponse.json({
          data: createMockEvents(requestedUserId || userId),
          mock: true
        });
      }

      console.log(`Returning ${typedData.length} security events`);
      return NextResponse.json({ data: typedData });
    } catch (err: any) {
      console.error('Error in security events API:', err);
      // Return mock data in case of error
      return NextResponse.json({
        data: createMockEvents(req.nextUrl.searchParams.get('userId') || 'unknown'),
        mock: true
      });
    }
  });
}

function createMockEvents(userId: string): SecurityEvent[] {
  return [
    {
      id: 'mock-1',
      event_type: 'auth.login',
      event_category: 'authentication',
      user_id: userId,
      ip_address: '***********',
      user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      location_city: 'San Francisco',
      location_region: 'California',
      location_country: 'United States',
      details: { action: 'login', success: true },
      created_at: new Date().toISOString()
    },
    {
      id: 'mock-2',
      event_type: 'suspicious.unusual_location',
      event_category: 'suspicious',
      user_id: userId,
      ip_address: '***********',
      user_agent: 'Mozilla/5.0 (Windows NT 10.0)',
      location_city: 'Unknown',
      location_region: 'Unknown',
      location_country: 'Unknown',
      details: { reason: 'Unusual location detected' },
      created_at: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 'mock-3',
      event_type: 'auth.logout',
      event_category: 'authentication',
      user_id: userId,
      ip_address: '***********',
      user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      location_city: 'San Francisco',
      location_region: 'California',
      location_country: 'United States',
      details: { action: 'logout', success: true },
      created_at: new Date(Date.now() - 7200000).toISOString()
    }
  ];
}
