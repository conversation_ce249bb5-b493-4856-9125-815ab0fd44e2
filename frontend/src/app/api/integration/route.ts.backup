// @ts-nocheck - API route type issues
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { withAuth } from '@/lib/auth-helpers';
import { createClient } from '@/lib/supabase/server';
import { CaseInsightsIntegration } from '@/lib/integration/case-insights';
import { DocumentInsightsIntegration } from '@/lib/integration/document-insights';
import { DeadlineInsightsIntegration } from '@/lib/integration/deadline-insights';
import { LLMProvider } from '@/lib/llm/service';
import { AuthUser } from '@/lib/types';

/**
 * API route handler for bi-directional integration between insights system and other components
 * Enables actioning insights across system boundaries and feeding back data to insight generation
 */
export const POST = withAuth(async (request: NextRequest, user: AuthUser, supabase: any, context: any) => {
  try {
    const supabase = createClient();
    const body = await request.json();
    const { action, entityType, entityId, data } = body;

    // Validation
    if (!action || !entityType) {
      return NextResponse.json({ message: 'Missing required fields' }, { status: 400 });
    }

    // Select appropriate integration service based on entity type
    let result;
    let success = false;

    // Initialize provider preference from query params or environment
    const { searchParams } = new URL(request.url);
    const providerParam = searchParams.get('provider') as LLMProvider | null;
    const preferredProvider =
      providerParam ||
      process.env.PREFERRED_LLM_PROVIDER as LLMProvider ||
      'openai';

    // Handle case-related integrations
    if (entityType === 'case') {
      const caseIntegration = new CaseInsightsIntegration(
        supabase,
        user.tenantId || '',
        user,
        { defaultProvider: preferredProvider }
      );

      switch (action) {
        case 'GENERATE_INSIGHTS':
          if (!entityId) {
            return NextResponse.json({ message: 'Case ID is required' }, { status: 400 });
          }
          result = await caseIntegration.enrichCaseWithInsights(entityId);
          success = result.caseUpdated;
          break;

        case 'APPLY_ACTION':
          if (!entityId || !data?.insightId || !data?.actionType) {
            return NextResponse.json({
              message: 'Missing required fields for case action'
            }, { status: 400 });
          }
          result = await caseIntegration.applyInsightAction(
            data.insightId,
            data.actionType,
            entityId,
            data.actionData || {}
          );
          success = result.success;
          break;

        case 'GET_STATUS_CHANGE_SUGGESTIONS':
          if (!entityId || !data?.oldStatus || !data?.newStatus) {
            return NextResponse.json({
              message: 'Missing required fields for status change suggestions'
            }, { status: 400 });
          }
          result = await caseIntegration.getSuggestionsForCaseStatusChange(
            entityId,
            data.oldStatus,
            data.newStatus
          );
          success = !!result;
          break;

        default:
          return NextResponse.json({
            message: `Unsupported action "${action}" for case entity`
          }, { status: 400 });
      }
    }
    // Handle document-related integrations
    else if (entityType === 'document') {
      const documentIntegration = new DocumentInsightsIntegration(
        supabase,
        user,
        { enableVectorSearch: !!process.env.PINECONE_API_KEY }
      );

      switch (action) {
        case 'FIND_RELATED_DOCUMENTS':
          if (!data?.insightText) {
            return NextResponse.json({
              message: 'Insight text is required to find related documents'
            }, { status: 400 });
          }
          result = await documentIntegration.findRelatedDocuments(
            data.insightText,
            data.caseId
          );
          success = result.documents.length > 0;
          break;

        case 'GENERATE_DOCUMENT_INSIGHTS':
          if (!entityId) {
            return NextResponse.json({ message: 'Document ID is required' }, { status: 400 });
          }
          result = await documentIntegration.enrichDocumentWithInsights(entityId);
          success = result.documentUpdated;
          break;

        case 'APPLY_INSIGHT_TO_DOCUMENT':
          if (!entityId || !data?.insightId || !data?.actionType) {
            return NextResponse.json({
              message: 'Missing required fields for document action'
            }, { status: 400 });
          }
          result = await documentIntegration.applyInsightToDocument(
            data.insightId,
            entityId,
            data.actionType,
            data.actionData || {}
          );
          success = result.success;
          break;

        default:
          return NextResponse.json({
            message: `Unsupported action "${action}" for document entity`
          }, { status: 400 });
      }
    }
    // Handle deadline-related integrations
    else if (entityType === 'deadline') {
      const deadlineIntegration = new DeadlineInsightsIntegration(
        supabase,
        user
      );

      switch (action) {
        case 'GENERATE_TIME_INSIGHTS':
          // Days ahead is optional with default in the implementation
          result = await deadlineIntegration.generateTimeBasedInsights(
            data?.daysAhead
          );
          success = result.insights.length > 0 || result.actions.length > 0;
          break;

        case 'CREATE_TASKS_FROM_INSIGHTS':
          if (!data?.actions || !Array.isArray(data.actions)) {
            return NextResponse.json({
              message: 'Actions array is required'
            }, { status: 400 });
          }
          result = await deadlineIntegration.createTasksFromInsights(data.actions);
          success = result.success;
          break;

        case 'ADD_CALENDAR_EVENT':
          if (!data?.insightId || !data?.eventData) {
            return NextResponse.json({
              message: 'Missing required fields for calendar event'
            }, { status: 400 });
          }
          result = await deadlineIntegration.addCalendarEvent(
            data.insightId,
            data.eventData
          );
          success = result.success;
          break;

        case 'GET_DEADLINE_RECOMMENDATIONS':
          if (!entityId || !data?.changeType) {
            return NextResponse.json({
              message: 'Missing required fields for deadline recommendations'
            }, { status: 400 });
          }
          result = await deadlineIntegration.getDeadlineChangeRecommendations(
            entityId,
            data.changeType
          );
          success = !!result.recommendations;
          break;

        default:
          return NextResponse.json({
            message: `Unsupported action "${action}" for deadline entity`
          }, { status: 400 });
      }
    } else {
      return NextResponse.json({
        message: `Unsupported entity type: ${entityType}`
      }, { status: 400 });
    }

    return NextResponse.json({
      success,
      result,
      meta: {
        timestamp: new Date().toISOString(),
        entityType,
        action,
        userId: user.id,
        provider: preferredProvider
      }
    }, { status: 200 });
  } catch (error) {
    console.error('[API Integration] Error handling integration request:', error);

    if (error instanceof Response) {
      return error;
    }

    return NextResponse.json({
      message: 'Internal Server Error in integration service',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
});
