import { NextRequest } from 'next/server';
import { createMocks } from 'node-mocks-http';
import * as neo4jClient from '@/lib/neo4j/client';
import * as openaiClient from '@/lib/openai/client';
import * as authHelpers from '@/lib/auth-helpers';

// Import the handler functions but mock the route exports
const routeModule = jest.requireActual('../route');

// Create mock handlers that we can spy on
const mockHandlers = {
  GET: jest.fn(routeModule.GET),
  POST: jest.fn(routeModule.POST)
};

// Mock the route module
jest.mock('../route', () => mockHandlers);

// Mock dependencies
jest.mock('@/lib/neo4j/client', () => ({
  getRecentUserActivities: jest.fn(),
  logUserFeedback: jest.fn()
}));

jest.mock('@/lib/openai/client', () => ({
  analyzeActivities: jest.fn(),
  getLLMStats: jest.fn(),
  clearLLMCache: jest.fn()
}));

jest.mock('@/lib/auth-helpers', () => ({
  withAuth: jest.fn((handler) => handler)
}));

describe('Insights API Route', () => {
  // Mock user for testing
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    role: 'attorney',
    tenantId: 'tenant-123'
  };

  // Mock activities
  const mockActivities = [
    {
      activityId: 'activity-1',
      userId: 'user-123',
      userRole: 'attorney',
      time: '2025-04-10T10:00:00Z',
      action: 'viewed_document',
      category: 'document',
      importance: 'high',
      tags: ['document', 'case-related'],
      summary: 'Reviewed settlement agreement',
      caseTitle: 'Smith v. Acme Corp',
      documentName: 'Settlement Agreement.pdf'
    },
    {
      activityId: 'activity-2',
      userId: 'user-123',
      userRole: 'attorney',
      time: '2025-04-09T15:30:00Z',
      action: 'sent_email',
      category: 'communication',
      importance: 'medium',
      tags: ['email', 'client'],
      summary: 'Sent status update to client',
      caseTitle: 'Smith v. Acme Corp'
    }
  ];

  // Mock AI-generated insights
  const mockAIInsights = [
    {
      id: 'insight-1',
      message: 'High Priority: Review settlement agreement for Smith v. Acme Corp',
      suggestions: ['View Case Details', 'Review Document'],
      priority: 10,
      groupKey: 'case:Smith v. Acme Corp',
      relatedActivities: ['activity-1'],
      relatedEntity: {
        type: 'case',
        name: 'Smith v. Acme Corp'
      },
      timestamp: '2025-04-10T10:00:00Z'
    }
  ];

  // Mock cache stats
  const mockCacheStats = {
    keys: 5,
    hits: 10,
    misses: 2
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    (neo4jClient.getRecentUserActivities as jest.Mock).mockResolvedValue(mockActivities);
    (openaiClient.analyzeActivities as jest.Mock).mockResolvedValue(mockAIInsights);
    (openaiClient.getLLMStats as jest.Mock).mockReturnValue(mockCacheStats);
    (neo4jClient.logUserFeedback as jest.Mock).mockResolvedValue('feedback-123');
  });

  // Helper function to create a mock request
  const createMockRequest = (method: 'GET' | 'POST', url: string, body?: unknown) => {
    const { req } = createMocks({
      method,
      url,
      body: body as any // Cast unknown to any for createMocks compatibility
    });

    // Add searchParams to mock NextRequest behavior
    const urlObj = new URL(url, 'http://localhost');
    Object.defineProperty(req, 'nextUrl', {
      get: () => urlObj
    });

    return req as unknown as NextRequest;
  };

  describe('GET handler', () => {
    it('should return AI-generated insights when useAi is true', async () => {
      // Create mock request with useAi=true
      const request = createMockRequest('GET', '/api/activity/insights?useAi=true');

      // Call the handler
      const response = await mockHandlers.GET(request, { params: { user: mockUser } });
      const responseData = await (response as Response).json();

      // Verify the response
      expect((response as Response).status).toBe(200);
      expect(responseData).toHaveProperty('insights');
      expect(responseData.insights).toEqual(mockAIInsights);
      expect(responseData.meta).toHaveProperty('aiGenerated', true);
      expect(responseData.meta).toHaveProperty('cacheStats', mockCacheStats);

      // Verify the correct functions were called
      expect(neo4jClient.getRecentUserActivities).toHaveBeenCalledWith({
        userId: mockUser.id,
        daysBack: 7, // Default
        limit: 15 // Default
      });
      expect(openaiClient.analyzeActivities).toHaveBeenCalled();
    });

    it('should use rule-based insights when useAi is false', async () => {
      // Create mock request with useAi=false
      const request = createMockRequest('GET', '/api/activity/insights?useAi=false');

      // Call the handler
      const response = await mockHandlers.GET(request, { params: { user: mockUser } });
      const responseData = await (response as Response).json();

      // Verify the response
      expect((response as Response).status).toBe(200);
      expect(responseData).toHaveProperty('insights');
      expect(responseData.meta).toHaveProperty('provider', 'rule-based');
      expect(responseData.meta).toHaveProperty('cacheStats', null);

      // Verify AI functions were not called
      expect(openaiClient.analyzeActivities).not.toHaveBeenCalled();
    });

    it('should use specified provider when provided', async () => {
      // Create mock request with provider=google
      const request = createMockRequest('GET', '/api/activity/insights?provider=google');

      // Call the handler
      await mockHandlers.GET(request, { params: { user: mockUser } });

      // Verify generateInsightsFromActivities was called with the correct provider
      expect(openaiClient.analyzeActivities).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          forceProvider: 'google'
        })
      );
    });

    it('should clear cache when requested by admin', async () => {
      // Create admin user
      const adminUser = { ...mockUser, role: 'admin' };

      // Create mock request with clearCache=true
      const request = createMockRequest('GET', '/api/activity/insights?clearCache=true');

      // Call the handler
      await mockHandlers.GET(request, { params: { user: adminUser } });

      // Verify cache was cleared
      expect(openaiClient.clearLLMCache).toHaveBeenCalled();
    });

    it('should not clear cache when requested by non-admin', async () => {
      // Create mock request with clearCache=true
      const request = createMockRequest('GET', '/api/activity/insights?clearCache=true');

      // Call the handler
      await mockHandlers.GET(request, { params: { user: mockUser } });

      // Verify cache was not cleared
      expect(openaiClient.clearLLMCache).not.toHaveBeenCalled();
    });

    it('should handle invalid parameters', async () => {
      // Create mock request with invalid parameters
      const request = createMockRequest('GET', '/api/activity/insights?daysBack=invalid&limit=invalid');

      // Call the handler
      const response = await mockHandlers.GET(request, { params: { user: mockUser } });

      // Verify the response
      expect((response as Response).status).toBe(400);
      const responseData = await (response as Response).json();
      expect(responseData).toHaveProperty('message', 'Invalid daysBack or limit parameter');
    });

    it('should handle empty activities', async () => {
      // Mock empty activities
      (neo4jClient.getRecentUserActivities as jest.Mock).mockResolvedValue([]);

      // Create mock request
      const request = createMockRequest('GET', '/api/activity/insights');

      // Call the handler
      const response = await mockHandlers.GET(request, { params: { user: mockUser } });
      const responseData = await (response as Response).json();

      // Verify the response
      expect((response as Response).status).toBe(200);
      expect(responseData.insights).toEqual([]);
    });

    it('should handle errors', async () => {
      // Mock error
      (neo4jClient.getRecentUserActivities as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Create mock request
      const request = createMockRequest('GET', '/api/activity/insights');

      // Call the handler
      const response = await mockHandlers.GET(request, { params: { user: mockUser } });

      // Verify the response
      expect((response as Response).status).toBe(500);
      const responseData = await (response as Response).json();
      expect(responseData).toHaveProperty('message', 'Internal Server Error fetching insights');
    });
  });

  describe('POST handler', () => {
    it('should log feedback successfully', async () => {
      // Create feedback data
      const feedbackData = {
        feedbackId: 'feedback-123',
        insightId: 'insight-123',
        action: 'clicked',
        rating: 5,
        comment: 'Very helpful insight'
      };

      // Create mock request
      const request = createMockRequest('POST', '/api/activity/insights', feedbackData);

      // Call the handler
      const response = await mockHandlers.POST(request, { params: { user: mockUser } });
      const responseData = await (response as Response).json();

      // Verify the response
      expect((response as Response).status).toBe(200);
      expect(responseData).toHaveProperty('success', true);
      expect(responseData).toHaveProperty('timestamp');

      // Verify feedback was logged
      expect(neo4jClient.logUserFeedback).toHaveBeenCalledWith({
        userId: mockUser.id,
        feedbackId: feedbackData.feedbackId,
        insightId: feedbackData.insightId,
        action: feedbackData.action,
        rating: feedbackData.rating,
        comment: feedbackData.comment,
        timestamp: expect.any(String)
      });
    });

    it('should handle missing required fields', async () => {
      // Create incomplete feedback data
      const feedbackData = {
        // Missing required fields
        action: 'clicked'
      };

      // Create mock request
      const request = createMockRequest('POST', '/api/activity/insights', feedbackData);

      // Call the handler
      const response = await mockHandlers.POST(request, { params: { user: mockUser } });

      // Verify the response
      expect((response as Response).status).toBe(400);
      const responseData = await (response as Response).json();
      expect(responseData).toHaveProperty('message', 'Missing required fields');
    });

    it('should log additional details for low ratings', async () => {
      // Create feedback data with low rating
      const feedbackData = {
        feedbackId: 'feedback-123',
        insightId: 'insight-123',
        action: 'rated',
        rating: 2,
        comment: 'Not very helpful'
      };

      // Create mock request
      const request = createMockRequest('POST', '/api/activity/insights', feedbackData);

      // Spy on console.warn
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

      // Call the handler
      await mockHandlers.POST(request, { params: { user: mockUser } });

      // Verify additional logging
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('Low rating'),
        expect.stringContaining(feedbackData.comment)
      );

      // Restore console.warn
      consoleWarnSpy.mockRestore();
    });

    it('should handle errors', async () => {
      // Mock error
      (neo4jClient.logUserFeedback as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Create feedback data
      const feedbackData = {
        feedbackId: 'feedback-123',
        insightId: 'insight-123',
        action: 'clicked'
      };

      // Create mock request
      const request = createMockRequest('POST', '/api/activity/insights', feedbackData);

      // Call the handler
      const response = await mockHandlers.POST(request, { params: { user: mockUser } });

      // Verify the response
      expect((response as Response).status).toBe(500);
      const responseData = await (response as Response).json();
      expect(responseData).toHaveProperty('message', 'Internal Server Error recording feedback');
    });
  });
});
