// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';
import { createClient, isAuthenticated, hasRole } from '@/lib/auth/auth-helper';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

/**
 * GET /api/admin/tenant-subscription
 * Get tenant subscriptions with pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    // Calculate offset
    const offset = (page - 1) * limit;

    // Build query
    let query = enhancedClient.tenants
      .from('tenant_subscriptions')
      .select(`
        *,
        subscription_plans!inner (
          name, code, base_price_monthly, base_price_yearly, features
        ),
        firms!inner (
          name, tenant_id
        )
      `, { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`firms.name.ilike.%${search}%`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1).order('created_at', { ascending: false });

    // Execute query
    const { data, count, error } = await query;

    if (error) {
      console.error('Error fetching tenant subscriptions:', error);
      return NextResponse.json({ error: 'Failed to fetch tenant subscriptions' }, { status: 500 });
    }

    return NextResponse.json({
      subscriptions: data,
      pagination: {
        total: count,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Error in tenant subscription API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/tenant-subscription
 * Create, update, or manage tenant subscriptions
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    const body = await request.json();

    // Determine the action to take
    const { action, data } = body;

    if (action === 'createSubscription') {
      // Create a new tenant subscription
      const now = new Date();
      const trialDays = data.trialDays || 0;

      let trialStart = null;
      let trialEnd = null;
      let status = 'active';

      if (trialDays > 0) {
        trialStart = now.toISOString();
        const trialEndDate = new Date(now);
        trialEndDate.setDate(trialEndDate.getDate() + trialDays);
        trialEnd = trialEndDate.toISOString();
        status = 'trialing';
      }

      // Calculate billing period
      const billingCycle = data.billingCycle || 'monthly';
      const currentPeriodStart = now.toISOString();
      const currentPeriodEnd = new Date(now);

      if (billingCycle === 'monthly') {
        currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
      } else {
        currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1);
      }

      const { data: subscription, error } = await enhancedClient.tenants
        .from('tenant_subscriptions')
        .insert({
          tenant_id: data.tenantId,
          plan_id: data.planId,
          status,
          billing_cycle: billingCycle,
          trial_start: trialStart,
          trial_end: trialEnd,
          current_period_start: currentPeriodStart,
          current_period_end: currentPeriodEnd.toISOString(),
          payment_provider: data.paymentProvider,
          payment_provider_subscription_id: data.paymentProviderSubscriptionId,
          metadata: data.metadata || {},
          created_at: now.toISOString(),
          updated_at: now.toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating tenant subscription:', error);
        return NextResponse.json({ error: 'Failed to create tenant subscription' }, { status: 500 });
      }

      return NextResponse.json({ subscription });
    }
    else if (action === 'updateSubscription') {
      // Update an existing tenant subscription
      const now = new Date();

      const updateData: any = {
        updated_at: now.toISOString(),
      };

      if (data.planId !== undefined) updateData.plan_id = data.planId;
      if (data.status !== undefined) updateData.status = data.status;
      if (data.billingCycle !== undefined) updateData.billing_cycle = data.billingCycle;
      if (data.trialEnd !== undefined) updateData.trial_end = data.trialEnd;
      if (data.currentPeriodStart !== undefined) updateData.current_period_start = data.currentPeriodStart;
      if (data.currentPeriodEnd !== undefined) updateData.current_period_end = data.currentPeriodEnd;
      if (data.canceledAt !== undefined) updateData.canceled_at = data.canceledAt;
      if (data.paymentProvider !== undefined) updateData.payment_provider = data.paymentProvider;
      if (data.paymentProviderSubscriptionId !== undefined) updateData.payment_provider_subscription_id = data.paymentProviderSubscriptionId;
      if (data.metadata !== undefined) updateData.metadata = data.metadata;

      const { data: subscription, error } = await enhancedClient.tenants
        .from('tenant_subscriptions')
        .update(updateData)
        .eq('id', data.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating tenant subscription:', error);
        return NextResponse.json({ error: 'Failed to update tenant subscription' }, { status: 500 });
      }

      return NextResponse.json({ subscription });
    }
    else if (action === 'cancelSubscription') {
      // Cancel a subscription
      const now = new Date();

      const updateData: any = {
        canceled_at: now.toISOString(),
        updated_at: now.toISOString(),
      };

      if (data.cancelImmediately) {
        updateData.status = 'canceled';
      }

      const { data: subscription, error } = await enhancedClient.tenants
        .from('tenant_subscriptions')
        .update(updateData)
        .eq('id', data.id)
        .select()
        .single();

      if (error) {
        console.error('Error canceling tenant subscription:', error);
        return NextResponse.json({ error: 'Failed to cancel tenant subscription' }, { status: 500 });
      }

      return NextResponse.json({ subscription });
    }
    else if (action === 'addAddon') {
      // Add an addon to a subscription
      const now = new Date();

      // Get the subscription to determine the billing period
      const { data: subscription, error: subscriptionError } = await enhancedClient.tenants
        .from('tenant_subscriptions')
        .select('current_period_start, current_period_end')
        .eq('id', data.subscriptionId)
        .single();

      if (subscriptionError) {
        console.error('Error fetching subscription:', subscriptionError);
        return NextResponse.json({ error: 'Failed to fetch subscription' }, { status: 500 });
      }

      const { data: addon, error } = await enhancedClient.tenants
        .from('tenant_addons')
        .insert({
          tenant_id: data.tenantId,
          subscription_id: data.subscriptionId,
          addon_id: data.addonId,
          status: 'active',
          quantity: data.quantity || 1,
          current_period_start: subscription.current_period_start,
          current_period_end: subscription.current_period_end,
          metadata: data.metadata || {},
          created_at: now.toISOString(),
          updated_at: now.toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error adding addon:', error);
        return NextResponse.json({ error: 'Failed to add addon' }, { status: 500 });
      }

      return NextResponse.json({ addon });
    }
    else if (action === 'updateAddon') {
      // Update an addon
      const now = new Date();

      const updateData: any = {
        updated_at: now.toISOString(),
      };

      if (data.status !== undefined) updateData.status = data.status;
      if (data.quantity !== undefined) updateData.quantity = data.quantity;
      if (data.canceledAt !== undefined) updateData.canceled_at = data.canceledAt;
      if (data.metadata !== undefined) updateData.metadata = data.metadata;

      const { data: addon, error } = await enhancedClient.tenants
        .from('tenant_addons')
        .update(updateData)
        .eq('id', data.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating addon:', error);
        return NextResponse.json({ error: 'Failed to update addon' }, { status: 500 });
      }

      return NextResponse.json({ addon });
    }
    else if (action === 'cancelAddon') {
      // Cancel an addon
      const now = new Date();

      const updateData: any = {
        canceled_at: now.toISOString(),
        updated_at: now.toISOString(),
      };

      if (data.cancelImmediately) {
        updateData.status = 'canceled';
      }

      const { data: addon, error } = await enhancedClient.tenants
        .from('tenant_addons')
        .update(updateData)
        .eq('id', data.id)
        .select()
        .single();

      if (error) {
        console.error('Error canceling addon:', error);
        return NextResponse.json({ error: 'Failed to cancel addon' }, { status: 500 });
      }

      return NextResponse.json({ addon });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error in tenant subscription API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
