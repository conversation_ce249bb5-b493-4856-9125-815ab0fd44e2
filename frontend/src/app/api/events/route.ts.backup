// @ts-nocheck - API route type issues
// frontend/src/app/api/events/route.ts
import { withAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth-helpers';
import type { SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { createServices } from '@/lib/services';
import { EventSchema } from '@/lib/services/calendar-event-services';

// GET /api/events - Get all calendar events for current tenant
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient
) => {
  try {
    // Parse query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get('start_date') || undefined;
    const endDate = url.searchParams.get('end_date') || undefined;
    const caseId = url.searchParams.get('case_id') || undefined;
    const eventType = url.searchParams.get('event_type') || undefined;
    const page = url.searchParams.get('page') ? parseInt(url.searchParams.get('page') as string) : 1;
    const limit = url.searchParams.get('limit') ? parseInt(url.searchParams.get('limit') as string) : 50;

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Use the calendar event service to get events with filtering
    const { events, totalCount } = await services.calendarEvents.getAll({
      start_date: startDate,
      end_date: endDate,
      case_id: caseId,
      event_type: eventType,
      page,
      limit
    });

    return NextResponse.json({
      events,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error in GET /api/events:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// POST /api/events - Create a new calendar event
export const POST = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient
) => {
  try {
    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Parse and validate request body
    const body = await req.json();
    const validationResult = EventSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Validation failed',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const validatedData = validationResult.data;

    // Create the calendar event using the calendar event service
    const createdEvent = await services.calendarEvents.create(user.id, validatedData);

    if (!createdEvent) {
      return NextResponse.json({ error: 'Failed to create calendar event' }, { status: 500 });
    }

    return NextResponse.json(createdEvent, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/events:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
