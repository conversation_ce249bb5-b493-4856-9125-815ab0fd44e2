// @ts-nocheck - API route type issues
// frontend/src/app/api/legal-templates/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, withServiceRole, AuthUser, UserRole } from '@/lib/auth-helpers';
import { SupabaseClient } from '@supabase/supabase-js';

// Define template types
interface LegalTemplate {
  id?: string;
  title: string;
  description?: string | null;
  content: string;
  location?: string | null;
  metadata?: Record<string, unknown>;
  created_by?: string | null;
  updated_by?: string | null;
  created_at?: string;
  updated_at?: string;
}

// **GET Handler: Fetch Legal Templates for Users with location filtering**
// Using withAuth to respect RLS policies for location-based filtering
export const GET = withAuth(async (req: NextRequest, user: AuthUser, supabase: SupabaseClient<any, "public", any>, context: Record<string, unknown>): Promise<Response> => {
  try {
    console.log('User accessing legal templates API:', {
      id: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId,
    });

    // Extract location from URL query if provided, default to user's location
    const { searchParams } = new URL(req.url);
    const locationFilter = searchParams.get('location');

    // Build query with the passed Supabase client that respects RLS
    let query = supabase
      .from('legal_templates')
      .select('*');

    // Apply additional location filter if provided in URL
    // Note: RLS will already apply basic location filtering based on user context
    if (locationFilter) {
      console.log(`Applying additional location filter: ${locationFilter}`);
      query = query.eq('location', locationFilter);
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching legal templates:', error);
      return NextResponse.json({
        error: 'Failed to fetch legal templates',
        details: error.message
      }, { status: 500 });
    }

    return NextResponse.json({ legal_templates: data });
  } catch (error) {
    console.error('Unexpected error in legal templates API:', error);
    return NextResponse.json({
      error: 'Server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}, ['partner', 'attorney', 'paralegal', 'staff', 'client'] as UserRole[]); // All roles can view templates

// **POST Handler: Create a New Legal Template (Service Role Operation)**
// Using withServiceRole since we need service role access to create templates
// No user authentication - this is for app managers with service role access
export const POST = withServiceRole(async (req: NextRequest, supabase: SupabaseClient<any, "public", any>, context: Record<string, unknown>): Promise<Response> => {
  try {
    // Parse request body
    let body: Partial<LegalTemplate> = {};
    try {
      body = await req.json();
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }

    // Get post data
    const { title, description, content, metadata, location, created_by } = body;

    // Validate required fields
    if (!title || !content) {
      return NextResponse.json({ error: 'Title and content are required fields' }, { status: 400 });
    }

    // Create template data
    const templateData: LegalTemplate = {
      title,
      description: description || null,
      content,
      location: location || null, // Store location for RLS filtering
      metadata: metadata || {},
      created_by: created_by || null, // No user context in withServiceRole
      updated_by: created_by || null
    };

    // Use the passed supabase client which is properly configured with service role
    const { data, error } = await supabase
      .from('legal_templates')
      .insert(templateData)
      .select()
      .single();

    if (error) {
      console.error('Error creating legal template:', error);
      return NextResponse.json({
        error: 'Failed to create template',
        details: error.message
      }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Legal template created successfully',
      template: data
    }, { status: 201 });
  } catch (error) {
    console.error('Unexpected error in legal templates API:', error);
    return NextResponse.json({
      error: 'Server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
});

// **PUT Handler: Update a Legal Template (Service Role Operation)**
// Using withServiceRole for app managers with service role access
export const PUT = withServiceRole(async (req: NextRequest, supabase: SupabaseClient<any, "public", any>, context: Record<string, unknown>): Promise<Response> => {
  try {
    // Parse request body
    let body: Partial<LegalTemplate> & { id: string } = { id: '' };
    try {
      body = await req.json();
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }

    // Get template ID and update data
    const { id, title, description, content, metadata, location, updated_by } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    // Create update data object, only including provided fields
    const updateData: Partial<LegalTemplate> = {
      updated_by: updated_by || null, // No user context in withServiceRole
      updated_at: new Date().toISOString()
    };

    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (content !== undefined) updateData.content = content;
    if (metadata !== undefined) updateData.metadata = metadata;
    if (location !== undefined) updateData.location = location;

    // Use the passed supabase client with service role
    const { data, error } = await supabase
      .from('legal_templates')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating legal template:', error);
      return NextResponse.json({
        error: 'Failed to update template',
        details: error.message
      }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Legal template updated successfully',
      template: data
    });
  } catch (error) {
    console.error('Unexpected error in legal templates API:', error);
    return NextResponse.json({
      error: 'Server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
});

// **DELETE Handler: Delete a Legal Template (Service Role Operation)**
// Using withServiceRole for app managers with service role access
export const DELETE = withServiceRole(async (req: NextRequest, supabase: SupabaseClient<any, "public", any>, context: Record<string, unknown>): Promise<Response> => {
  try {
    // Get template ID from URL
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    // Validate template ID
    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    // Delete the template
    const { error } = await supabase
      .from('legal_templates')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting legal template:', error);
      return NextResponse.json({
        error: 'Failed to delete template',
        details: error.message
      }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Legal template deleted successfully'
    });
  } catch (error) {
    console.error('Unexpected error in legal templates API:', error);
    return NextResponse.json({
      error: 'Server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
});
