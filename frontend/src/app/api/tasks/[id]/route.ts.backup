// frontend/src/app/api/tasks/[id]/route.ts
import { withAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth-helpers';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import * as z from 'zod';
import { createServices } from '@/lib/services';

// GET /api/tasks/[id] - Get a specific task by ID
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
) => {
  try {
    // Safely extract and validate the id parameter
    const params = context.params;
    if (typeof params !== 'object' || params === null || !('id' in params) || typeof params.id !== 'string') {
      return NextResponse.json({ error: 'Task ID parameter is missing or invalid' }, { status: 400 });
    }
    const id = params.id;

    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid task ID format' }, { status: 400 });
    }

    // Check for relation inclusion from query params
    const { searchParams } = new URL(req.url);
    const includeRelations = searchParams.get('include') === 'relations';

    // Build select query based on whether relations are requested
    // Use proper foreign key constraint names for joins
    const selectQuery = includeRelations
      ? '*, users!tasks_assigned_to_tenant_user_fkey(id, email, first_name, last_name), case:related_case(id, title)'
      : '*';

    // Use schema('tenants') to ensure proper schema access
    const { data, error } = await (supabase as any)
      .schema('tenants')
      .from('tasks')
      .select(selectQuery)
      .eq('id', id)
      .eq('tenant_id', user.tenantId || '')
      .single();

    if (error) {
      console.error('Error fetching task:', error);
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Task not found' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Failed to fetch task' }, { status: 500 });
    }

    if (!data) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error('Error in GET /api/tasks/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// PUT /api/tasks/[id] - Update a task
export const PUT = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
) => {
  try {
    // Safely extract and validate the id parameter
    const params = context.params;
    if (typeof params !== 'object' || params === null || !('id' in params) || typeof params.id !== 'string') {
      return NextResponse.json({ error: 'Task ID parameter is missing or invalid' }, { status: 400 });
    }
    const id = params.id;

    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid task ID format' }, { status: 400 });
    }

    // Get request body
    const data = await req.json();

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase as TypedSupabaseClient, user.tenantId || '');

    // Update the task using the task service
    // The service handles all related data updates and history tracking
    const updatedTask = await services.tasks.update(id, user.id, data);

    if (!updatedTask) {
      return NextResponse.json({ error: 'Task not found or update failed' }, { status: 404 });
    }

    return NextResponse.json(updatedTask);
  } catch (error: unknown) {
    console.error('Error in PUT /api/tasks/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// DELETE /api/tasks/[id] - Delete a task
export const DELETE = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
) => {
  try {
    // Safely extract and validate the id parameter
    const params = context.params;
    if (typeof params !== 'object' || params === null || !('id' in params) || typeof params.id !== 'string') {
      return NextResponse.json({ error: 'Task ID parameter is missing or invalid' }, { status: 400 });
    }
    const id = params.id;

    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid task ID format' }, { status: 400 });
    }

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase as TypedSupabaseClient, user.tenantId || '');

    // Delete the task using the task service
    // The service handles fetching existing task and history tracking internally
    const success = await services.tasks.delete(id, user.id);

    if (!success) {
      return NextResponse.json({ error: 'Task not found or delete failed' }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    console.error('Error in DELETE /api/tasks/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
