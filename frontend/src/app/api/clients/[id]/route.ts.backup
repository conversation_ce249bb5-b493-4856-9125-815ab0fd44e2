// @ts-nocheck - API route type issues
//frontend/src/app/api/clients/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import { createServices } from '@/lib/services';
import { z } from 'zod';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Define interfaces for client data
interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone_primary?: string;
  client_type: 'individual' | 'business';
  intake_date?: string;
  status: 'active' | 'inactive' | 'pending';
  business_name?: string;
  business_type?: string;
  tax_id?: string;
  date_of_birth?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
  };
  assigned_attorney_id?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  tenant_id: string;
}

interface UpdateClientData {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_primary?: string;
  client_type?: 'individual' | 'business';
  intake_date?: string;
  status?: 'active' | 'inactive' | 'pending';
  business_name?: string;
  business_type?: string;
  tax_id?: string;
  date_of_birth?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
  };
  assigned_attorney_id?: string;
}

// We'll use the schema from the client service
// The previous schema is commented out for reference
/*
const UpdateClientSchema = z.object({
  first_name: z.string().min(1, 'First name is required').optional(),
  last_name: z.string().min(1, 'Last name is required').optional(),
  email: z.string().email('Invalid email address').optional(),
  phone_primary: z.string().optional(),
  client_type: z.enum(['individual', 'business']).optional(),
  intake_date: z.string().optional(),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  business_name: z.string().optional(),
  business_type: z.string().optional(),
  tax_id: z.string().optional(),
  date_of_birth: z.string().optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zip: z.string().optional(),
  }).optional(),
  assigned_attorney_id: z.string().uuid().optional(),
});
*/

// GET /api/clients/[id] - Fetch a specific client by ID
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const params = context.params as { id: string } | undefined;

    if (!params || !params.id || !z.string().uuid().safeParse(params.id).success) {
      return NextResponse.json({ error: 'Invalid client ID format' }, { status: 400 });
    }

    // Create services instance
    const services = createServices(supabase, user.tenantId);

    try {
      // Get client with detailed information
      const client = await services.clients.getById(params.id);
      return NextResponse.json(client);
    } catch (error: any) {
      if (error.message === 'Client not found') {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }
      throw error;
    }
  } catch (error: any) {
    console.error('Error in GET /api/clients/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

// PUT /api/clients/[id] - Update a client
export const PUT = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const params = context.params as { id: string } | undefined;

    if (!params || !params.id || !z.string().uuid().safeParse(params.id).success) {
      return NextResponse.json({ error: 'Invalid client ID format' }, { status: 400 });
    }

    const body = await req.json() as UpdateClientData;

    // Create services instance
    const services = createServices(supabase, user.tenantId);

    try {
      // Update client using the service
      const updatedClient = await services.clients.update(params.id, body, user.id);
      return NextResponse.json(updatedClient);
    } catch (error: any) {
      if (error.message === 'Client not found') {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      } else if (error.name === 'ZodError') {
        return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
      }
      throw error;
    }
  } catch (error: any) {
    console.error('Error in PUT /api/clients/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

// DELETE /api/clients/[id] - Delete a client
export const DELETE = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const params = context.params as { id: string } | undefined;

    if (!params || !params.id || !z.string().uuid().safeParse(params.id).success) {
      return NextResponse.json({ error: 'Invalid client ID format' }, { status: 400 });
    }

    // Create services instance
    const services = createServices(supabase, user.tenantId);

    try {
      // Delete client using the service (true = hard delete)
      const success = await services.clients.delete(params.id, user.id, true);

      if (success) {
        return NextResponse.json({ message: 'Client deleted successfully' });
      } else {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }
    } catch (error: any) {
      if (error.message === 'Client not found') {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }
      throw error;
    }
  } catch (error: any) {
    console.error('Error in DELETE /api/clients/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}, ['partner', 'attorney', 'paralegal', 'staff']);
