// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import { UserRole } from '@/lib/types/auth';
import { createServiceClient } from '@/lib/auth-helpers';
import { SupabaseClient } from '@supabase/supabase-js';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

// Define interfaces for document highlights
interface DocumentHighlight {
  id?: string;
  tenant_id: string;
  document_id: string;
  document_type: string;
  start_offset: number;
  end_offset: number;
  highlight_color?: string;
  comment?: string;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

// Allowed roles for creating/updating/deleting highlights
const EDIT_ROLES = ['partner', 'attorney', 'paralegal', 'staff'] as UserRole[];

/**
 * GET: List all highlights for a document (auth required, tenant/role enforced)
 * POST: Create a new highlight (auth required, tenant/role enforced)
 */
export const GET = withAuth(async (
  req: NextRequest,
  authUser: AuthUser,
  supabase: SupabaseClient<any, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  const { searchParams } = new URL(req.url);
  const document_id = searchParams.get('document_id');
  const document_type = searchParams.get('document_type');
  if (!document_id || !document_type) {
    return NextResponse.json({ error: 'Missing document_id or document_type' }, { status: 400 });
  }

  const serviceClient = createServiceClient(req);

  // Create a custom schema for document highlights
  const customSchema = {
    name: 'document_highlights',
    schema: 'public'
  };

  // Use the enhanced client with the custom schema
  const { data, error } = await serviceClient
    .from(customSchema.name as any)
    .select('*')
    .eq('tenant_id', authUser.tenantId)
    .eq('document_id', document_id)
    .eq('document_type', document_type)
    .order('created_at', { ascending: true });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data);
});

export const POST = withAuth(async (
  req: NextRequest,
  authUser: AuthUser,
  supabase: SupabaseClient<any, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  if (!EDIT_ROLES.includes(authUser.role as UserRole)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }

  const body = await req.json();
  const { document_id, document_type, start_offset, end_offset, highlight_color, comment } = body;

  if (!document_id || !document_type || start_offset == null || end_offset == null) {
    return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
  }

  const serviceClient = createServiceClient(req);

  // Create a custom schema for document highlights
  const customSchema = {
    name: 'document_highlights',
    schema: 'public'
  };

  const highlightData: DocumentHighlight = {
    tenant_id: authUser.tenantId,
    document_id,
    document_type,
    start_offset,
    end_offset,
    highlight_color,
    comment,
    created_by: authUser.id,
  };

  const { data, error } = await serviceClient
    .from(customSchema.name as any)
    .insert([highlightData as any])
    .select();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data[0]);
}, EDIT_ROLES);

export const PATCH = withAuth(async (
  req: NextRequest,
  authUser: AuthUser,
  supabase: SupabaseClient<any, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  if (!EDIT_ROLES.includes(authUser.role as UserRole)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }

  const body = await req.json();
  const { id, comment, highlight_color, start_offset, end_offset } = body;

  if (!id) {
    return NextResponse.json({ error: 'Missing highlight id' }, { status: 400 });
  }

  const serviceClient = createServiceClient(req);

  // Create a custom schema for document highlights
  const customSchema = {
    name: 'document_highlights',
    schema: 'public'
  };

  const updateData: Partial<DocumentHighlight> = {
    updated_at: new Date().toISOString()
  };

  if (comment !== undefined) updateData.comment = comment;
  if (highlight_color !== undefined) updateData.highlight_color = highlight_color;
  if (start_offset !== undefined) updateData.start_offset = start_offset;
  if (end_offset !== undefined) updateData.end_offset = end_offset;

  const { data, error } = await serviceClient
    .from(customSchema.name as any)
    .update(updateData as any)
    .eq('id', id)
    .eq('tenant_id', authUser.tenantId)
    .eq('created_by', authUser.id)
    .select();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data[0]);
}, EDIT_ROLES);

export const DELETE = withAuth(async (
  req: NextRequest,
  authUser: AuthUser,
  supabase: SupabaseClient<any, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  if (!EDIT_ROLES.includes(authUser.role as UserRole)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }

  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json({ error: 'Missing highlight id' }, { status: 400 });
  }

  const serviceClient = createServiceClient(req);

  // Create a custom schema for document highlights
  const customSchema = {
    name: 'document_highlights',
    schema: 'public'
  };

  const { error } = await serviceClient
    .from(customSchema.name as any)
    .delete()
    .eq('id', id)
    .eq('tenant_id', authUser.tenantId)
    .eq('created_by', authUser.id);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ success: true });
}, EDIT_ROLES);
