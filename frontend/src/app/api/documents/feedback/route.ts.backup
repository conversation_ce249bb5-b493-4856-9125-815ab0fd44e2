// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import { Database } from '@/lib/database.types';
import type { SupabaseClient } from '@supabase/supabase-js';

// Define interfaces for feedback data
interface FeedbackData {
  document_id: string;
  document_type: 'authored' | 'case' | 'client';
  rating: number;
  comment?: string;
}

/**
 * POST /api/documents/feedback
 * Body: { document_id: string, document_type: 'authored' | 'case' | 'client', rating: number, comment?: string }
 * Authenticated: Yes
 */
export const POST = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const body = await req.json() as FeedbackData;
    const { document_id, document_type, rating, comment } = body;

    if (!document_id || !document_type || typeof rating !== 'number') {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }

    // Only allow specific document types
    if (!['authored', 'case', 'client'].includes(document_type)) {
      return NextResponse.json({ error: 'Invalid document_type.' }, { status: 400 });
    }

    // Insert feedback into tenants.document_summary_feedback
    const { error } = await supabase
      .schema('tenants')
      .from('document_summary_feedback')
      .insert([
        {
          document_id,
          document_type,
          user_id: user.id,
          rating,
          comment: comment || null,
          created_at: new Date().toISOString(),
        },
      ]);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    console.error('Error saving document feedback:', error);
    let errorMessage = 'Failed to save feedback';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json(
      { error: 'Failed to save feedback', details: errorMessage },
      { status: 500 }
    );
  }
}, ['partner', 'attorney', 'paralegal', 'staff', 'client']); // Allow all authenticated users to provide feedback
