// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from "next/server";
import { createServiceClient } from "@/lib/auth-helpers";
import { withAuth, AuthUser } from "@/lib/auth-helpers";
import { z } from "zod";
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

// Define interfaces for search data
interface SearchResult {
  document_id: string;
  score: number;
  metadata: {
    document_type: string;
    title?: string;
    case_id?: string;
    chunk_num: number;
    chunk_count: number;
    content_preview?: string;
    created_at?: string;
  };
}

interface SearchResponse {
  results: SearchResult[];
}

// Schema for document search requests
const documentSearchSchema = z.object({
  query: z.string().min(1, "Search query is required"),
  caseId: z.string().optional(),
  documentType: z.string().optional(),
  limit: z.number().min(1).max(50).default(10),
  includePreview: z.boolean().default(true),
});

type DocumentSearchRequest = z.infer<typeof documentSearchSchema>;

// Define specific types for clarity
type CaseAssignment = Database['tenants']['Tables']['case_assignments']['Row'];
type Case = Database['tenants']['Tables']['cases']['Row'];

/**
 * Semantic document search endpoint
 * Searches for documents using vector embeddings in Pinecone
 */
export const POST = withAuth(
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    // Parse and validate the request body
    let requestData: DocumentSearchRequest;
    try {
      const body = await req.json();
      requestData = documentSearchSchema.parse(body);
    } catch (error) {
      console.error("Invalid search request:", error);
      return NextResponse.json(
        { error: "Invalid request parameters" },
        { status: 400 }
      );
    }

    try {
      // Create a Supabase client with service role for administrative operations
      const supabase = createServiceClient(req);

      // Enhance the client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Prepare an access control list for the user based on role
      let accessibleCaseIds: string[] = [];

      // If a specific case is requested, check if user has access to it
      if (requestData.caseId) {
        // For partners, allow access to all cases in their tenant
        if (user.role === "partner") {
          const { data: caseData, error: caseError } = await enhancedClient.tenants
            .from("cases")
            .select("id")
            .eq("id", requestData.caseId)
            .eq("tenant_id", user.tenantId)
            .single();

          if (caseError || !caseData) {
            return NextResponse.json(
              { error: "Case not found or you don't have access" },
              { status: 403 }
            );
          }

          accessibleCaseIds = [requestData.caseId];
        } else {
          // For other roles, check assignments
          const { data: assignments, error: assignmentError } = await enhancedClient.tenants
            .from("case_assignments")
            .select("case_id")
            .eq("user_id", user.id)
            .eq("tenant_id", user.tenantId)
            .eq("case_id", requestData.caseId);

          if (assignmentError || !assignments?.length) {
            return NextResponse.json(
              { error: "You don't have access to this case" },
              { status: 403 }
            );
          }

          accessibleCaseIds = assignments.map((a: CaseAssignment) => a.case_id);
        }
      } else {
        // If no specific case, get all accessible cases for the user
        if (user.role === "partner") {
          // Partners can access all cases in their tenant
          const { data: cases, error: casesError } = await enhancedClient.tenants
            .from("cases")
            .select("id")
            .eq("tenant_id", user.tenantId);

          if (!casesError && cases) {
            accessibleCaseIds = cases.map((c: Case) => c.id);
          }
        } else {
          // Other roles can only access assigned cases
          const { data: assignments, error: assignmentError } = await enhancedClient.tenants
            .from("case_assignments")
            .select("case_id")
            .eq("user_id", user.id)
            .eq("tenant_id", user.tenantId);

          if (!assignmentError && assignments) {
            accessibleCaseIds = assignments.map((a: CaseAssignment) => a.case_id);
          }
        }
      }

      // Call our document search service - this will be a Python-based API
      const searchResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/document_search`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.API_SERVICE_TOKEN}`
        },
        body: JSON.stringify({
          query: requestData.query,
          tenant_id: user.tenantId,
          user_id: user.id,
          role: user.role,
          accessible_case_ids: accessibleCaseIds,
          document_type: requestData.documentType,
          limit: requestData.limit,
          include_preview: requestData.includePreview
        })
      });

      if (!searchResponse.ok) {
        const errorData = await searchResponse.json();
        console.error("Search API error:", errorData);
        return NextResponse.json(
          { error: "Error searching documents" },
          { status: searchResponse.status }
        );
      }

      const searchResults = await searchResponse.json() as SearchResponse;

      // Transform results to our API format
      const results = searchResults.results.map((result: SearchResult) => ({
        id: result.document_id,
        score: result.score,
        documentType: result.metadata.document_type,
        title: result.metadata.title || "Untitled Document",
        caseId: result.metadata.case_id,
        chunkNum: result.metadata.chunk_num,
        chunkCount: result.metadata.chunk_count,
        preview: result.metadata.content_preview,
        createdAt: result.metadata.created_at,
      }));

      // Log this search for compliance
      await enhancedClient.tenants
        .from("search_logs")
        .insert({
          user_id: user.id,
          tenant_id: user.tenantId,
          query: requestData.query,
          result_count: results.length,
          case_id: requestData.caseId,
          document_type: requestData.documentType,
        } as any);

      return NextResponse.json({
        results,
        count: results.length,
        query: requestData.query
      });
    } catch (error: unknown) {
      console.error('Error in document search:', error);
      let errorMessage = 'An error occurred while searching documents.';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return NextResponse.json(
        { error: 'Document search failed', details: errorMessage },
        { status: 500 }
      );
    }
  },
  ["partner", "attorney", "paralegal", "staff", "client"]
);
