#!/bin/bash

# Auth Import Migration Script
# Migrates all auth helper imports to use the new modular auth structure

echo "🚀 Starting auth import migration..."

# Find all TypeScript files that need migration
FILES=$(find frontend/src -name "*.ts" -o -name "*.tsx" | xargs grep -l "from.*auth-helpers\|from.*auth/auth-helper" | sort)

if [ -z "$FILES" ]; then
    echo "✅ No files found that need migration"
    exit 0
fi

echo "📁 Found $(echo "$FILES" | wc -l) files to migrate:"
echo "$FILES"
echo ""

# Counter for tracking progress
TOTAL=$(echo "$FILES" | wc -l)
CURRENT=0

# Process each file
for file in $FILES; do
    CURRENT=$((CURRENT + 1))
    echo "[$CURRENT/$TOTAL] Processing: $file"
    
    # Create backup
    cp "$file" "$file.backup"
    
    # Apply transformations
    sed -i '' \
        -e "s|from '@/lib/auth-helpers'|from '@/lib/auth'|g" \
        -e "s|from '@/lib/auth-helpers/index'|from '@/lib/auth'|g" \
        -e "s|from '@/lib/auth/auth-helper'|from '@/lib/auth'|g" \
        -e "s|from '@/lib/auth/jwt-helper'|from '@/lib/auth'|g" \
        "$file"
    
    # Handle specific function renames for legacy compatibility
    # These are for files that use the old function signatures
    sed -i '' \
        -e "s|isAuthenticated,|isAuthenticatedLegacy as isAuthenticated,|g" \
        -e "s|hasRole,|hasRoleLegacy as hasRole,|g" \
        -e "s|verifyJWT,|verifyJWT,|g" \
        "$file"
    
    # Handle import consolidation - merge UserRole imports
    sed -i '' \
        -e "s|import { \(.*\) } from '@/lib/auth';\nimport { UserRole } from '@/lib/types/auth';|import { \1, UserRole } from '@/lib/auth';|g" \
        "$file"
    
    echo "  ✅ Updated imports in $file"
done

echo ""
echo "🧹 Cleaning up import statements..."

# Second pass to clean up any remaining issues
for file in $FILES; do
    # Remove duplicate UserRole imports
    sed -i '' \
        -e '/import.*UserRole.*@\/lib\/types\/auth/d' \
        "$file"
    
    # Consolidate multiple imports from @/lib/auth
    python3 -c "
import re
import sys

file_path = '$file'
with open(file_path, 'r') as f:
    content = f.read()

# Find all imports from @/lib/auth
auth_imports = re.findall(r'import\s*{\s*([^}]+)\s*}\s*from\s*[\'\"]\@\/lib\/auth[\'\"]\s*;', content)

if len(auth_imports) > 1:
    # Combine all imports
    all_imports = []
    for imp in auth_imports:
        all_imports.extend([i.strip() for i in imp.split(',')])
    
    # Remove duplicates while preserving order
    unique_imports = []
    seen = set()
    for imp in all_imports:
        if imp not in seen:
            unique_imports.append(imp)
            seen.add(imp)
    
    # Create consolidated import
    consolidated = 'import { ' + ', '.join(unique_imports) + ' } from \'@/lib/auth\';'
    
    # Remove all existing @/lib/auth imports
    content = re.sub(r'import\s*{\s*[^}]+\s*}\s*from\s*[\'\"]\@\/lib\/auth[\'\"]\s*;\s*\n?', '', content)
    
    # Add consolidated import at the top (after other imports)
    lines = content.split('\n')
    import_end = 0
    for i, line in enumerate(lines):
        if line.strip().startswith('import ') and '@/lib/auth' not in line:
            import_end = i + 1
    
    lines.insert(import_end, consolidated)
    content = '\n'.join(lines)
    
    with open(file_path, 'w') as f:
        f.write(content)
"
done

echo ""
echo "🔍 Verifying migration..."

# Check for any remaining old imports
REMAINING=$(find frontend/src -name "*.ts" -o -name "*.tsx" | xargs grep -l "from.*auth-helpers\|from.*auth/auth-helper" 2>/dev/null | grep -v ".backup" || true)

if [ -n "$REMAINING" ]; then
    echo "⚠️  Warning: Some files still have old imports:"
    echo "$REMAINING"
else
    echo "✅ All imports successfully migrated!"
fi

echo ""
echo "🧪 Running TypeScript check..."
cd frontend && npm run type-check 2>/dev/null || echo "⚠️  TypeScript check failed - manual fixes may be needed"

echo ""
echo "✨ Migration complete!"
echo "📝 Backup files created with .backup extension"
echo "🗑️  To remove backups: find frontend/src -name '*.backup' -delete"
