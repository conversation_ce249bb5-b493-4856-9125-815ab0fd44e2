#!/usr/bin/env node

/**
 * Comprehensive Authentication Refactor Testing
 * Tests JWT claims, client creation, and auth flows
 */

const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

// Test configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_KEY;

console.log('🧪 COMPREHENSIVE AUTHENTICATION REFACTOR TESTING\n');

// Test 1: JWT Claims Structure
async function testJWTClaimsStructure() {
  console.log('📋 TEST 1: JWT Claims Structure');
  console.log('=====================================');

  try {
    // Create a mock JWT token with our expected structure
    const mockPayload = {
      sub: 'c35cfd89-faad-4621-bc3a-dc1c26ad891c',
      email: '<EMAIL>',
      role: 'attorney',
      tenant_id: 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11',
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000),
      jti: 'test-jwt-id'
    };

    const secret = 'test-secret';
    const token = jwt.sign(mockPayload, secret);

    console.log('🔑 Mock JWT Token Created');
    console.log('📝 Expected Claims:', JSON.stringify(mockPayload, null, 2));

    // Test JWT parsing (simulate our new auth structure)
    const decoded = jwt.decode(token);
    console.log('🔍 Decoded Claims:', JSON.stringify(decoded, null, 2));

    // Verify critical claims
    const tests = [
      { name: 'Subject (sub)', value: decoded.sub, expected: mockPayload.sub },
      { name: 'Email', value: decoded.email, expected: mockPayload.email },
      { name: 'Role', value: decoded.role, expected: mockPayload.role },
      { name: 'Tenant ID', value: decoded.tenant_id, expected: mockPayload.tenant_id },
      { name: 'Expiration', value: decoded.exp, expected: mockPayload.exp },
    ];

    let passed = 0;
    tests.forEach(test => {
      const success = test.value === test.expected;
      console.log(`${success ? '✅' : '❌'} ${test.name}: ${test.value} ${success ? '==' : '!='} ${test.expected}`);
      if (success) passed++;
    });

    console.log(`\n📊 JWT Claims Test: ${passed}/${tests.length} passed\n`);
    return passed === tests.length;

  } catch (error) {
    console.error('❌ JWT Claims Test Failed:', error.message);
    return false;
  }
}

// Test 2: Supabase Client Creation
async function testSupabaseClientCreation() {
  console.log('🔌 TEST 2: Supabase Client Creation');
  console.log('=====================================');

  const results = [];

  try {
    // Test 1: Anonymous Client
    console.log('🔓 Testing Anonymous Client...');
    const anonClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    if (anonClient && anonClient.auth) {
      console.log('✅ Anonymous client created successfully');
      console.log(`   URL: ${anonClient.supabaseUrl}`);
      console.log(`   Key: ${anonClient.supabaseKey.substring(0, 20)}...`);
      results.push(true);
    } else {
      console.log('❌ Anonymous client creation failed');
      results.push(false);
    }

    // Test 2: Service Role Client
    if (SUPABASE_SERVICE_KEY) {
      console.log('\n🔑 Testing Service Role Client...');
      const serviceClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
      
      if (serviceClient && serviceClient.auth) {
        console.log('✅ Service role client created successfully');
        console.log(`   URL: ${serviceClient.supabaseUrl}`);
        console.log(`   Key: ${serviceClient.supabaseKey.substring(0, 20)}...`);
        results.push(true);
      } else {
        console.log('❌ Service role client creation failed');
        results.push(false);
      }
    } else {
      console.log('⚠️  Service key not available, skipping service client test');
      results.push(true); // Don't fail if service key not available
    }

    // Test 3: Client Configuration
    console.log('\n⚙️  Testing Client Configuration...');
    const testClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      auth: {
        autoRefreshToken: true,
        persistSession: true
      }
    });

    if (testClient && testClient.auth) {
      console.log('✅ Client with custom config created successfully');
      results.push(true);
    } else {
      console.log('❌ Client with custom config creation failed');
      results.push(false);
    }

    const passed = results.filter(r => r).length;
    console.log(`\n📊 Client Creation Test: ${passed}/${results.length} passed\n`);
    return passed === results.length;

  } catch (error) {
    console.error('❌ Client Creation Test Failed:', error.message);
    return false;
  }
}

// Test 3: Auth Function Compatibility
async function testAuthFunctionCompatibility() {
  console.log('🔄 TEST 3: Auth Function Compatibility');
  console.log('=====================================');

  try {
    // Simulate the old vs new function signatures
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      role: 'attorney',
      tenantId: 'test-tenant-id',
      metadata: { roles: ['attorney'] }
    };

    const mockSupabaseUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      user_metadata: {
        role: 'attorney',
        tenant_id: 'test-tenant-id',
        roles: ['attorney']
      }
    };

    // Test role checking compatibility
    console.log('👮 Testing Role Checking Functions...');
    
    // Simulate hasRole function (new)
    function hasRole(user, roles) {
      if (!user) return false;
      if (user.role && roles.includes(user.role)) return true;
      if (user.metadata?.roles && Array.isArray(user.metadata.roles)) {
        return user.metadata.roles.some(role => roles.includes(role));
      }
      return false;
    }

    // Simulate hasRoleLegacy function (for Supabase users)
    function hasRoleLegacy(user, roles) {
      if (!user) return false;
      const userRole = user.role || user.user_metadata?.role;
      if (userRole && roles.includes(userRole)) return true;
      if (user.user_metadata?.roles && Array.isArray(user.user_metadata.roles)) {
        return user.user_metadata.roles.some(role => roles.includes(role));
      }
      return false;
    }

    const testRoles = ['attorney', 'partner'];
    
    const newResult = hasRole(mockUser, testRoles);
    const legacyResult = hasRoleLegacy(mockSupabaseUser, testRoles);
    
    console.log(`✅ New hasRole: ${newResult}`);
    console.log(`✅ Legacy hasRole: ${legacyResult}`);
    console.log(`${newResult === legacyResult ? '✅' : '❌'} Results match: ${newResult === legacyResult}`);

    // Test authentication checking
    console.log('\n🔐 Testing Authentication Functions...');
    
    function isAuthenticated(user) {
      return !!user && !!user.id;
    }

    function isAuthenticatedLegacy(user) {
      return !!user && !!user.id;
    }

    const authResult = isAuthenticated(mockUser);
    const authLegacyResult = isAuthenticatedLegacy(mockSupabaseUser);
    
    console.log(`✅ New isAuthenticated: ${authResult}`);
    console.log(`✅ Legacy isAuthenticated: ${authLegacyResult}`);
    console.log(`${authResult === authLegacyResult ? '✅' : '❌'} Results match: ${authResult === authLegacyResult}`);

    console.log('\n📊 Function Compatibility Test: ✅ PASSED\n');
    return true;

  } catch (error) {
    console.error('❌ Function Compatibility Test Failed:', error.message);
    return false;
  }
}

// Test 4: Multi-Tenant Isolation
async function testMultiTenantIsolation() {
  console.log('🏢 TEST 4: Multi-Tenant Isolation');
  console.log('=====================================');

  try {
    // Simulate different tenant scenarios
    const tenant1User = {
      id: 'user1',
      email: '<EMAIL>',
      role: 'attorney',
      tenantId: 'tenant-1'
    };

    const tenant2User = {
      id: 'user2',
      email: '<EMAIL>',
      role: 'attorney',
      tenantId: 'tenant-2'
    };

    const superAdminUser = {
      id: 'admin',
      email: '<EMAIL>',
      role: 'superadmin',
      tenantId: null
    };

    // Test tenant access function
    function canAccessTenant(user, tenantId) {
      if (!user) return false;
      
      // Super admins can access any tenant
      const SUPER_ADMIN_EMAILS = ['<EMAIL>'];
      if (user.role === 'superadmin' || SUPER_ADMIN_EMAILS.includes(user.email)) {
        return true;
      }
      
      // Users can only access their own tenant
      return user.tenantId === tenantId;
    }

    console.log('🔍 Testing Tenant Access Control...');
    
    const tests = [
      { user: tenant1User, tenant: 'tenant-1', expected: true, desc: 'User 1 accessing own tenant' },
      { user: tenant1User, tenant: 'tenant-2', expected: false, desc: 'User 1 accessing other tenant' },
      { user: tenant2User, tenant: 'tenant-2', expected: true, desc: 'User 2 accessing own tenant' },
      { user: tenant2User, tenant: 'tenant-1', expected: false, desc: 'User 2 accessing other tenant' },
      { user: superAdminUser, tenant: 'tenant-1', expected: true, desc: 'Super admin accessing tenant 1' },
      { user: superAdminUser, tenant: 'tenant-2', expected: true, desc: 'Super admin accessing tenant 2' },
    ];

    let passed = 0;
    tests.forEach(test => {
      const result = canAccessTenant(test.user, test.tenant);
      const success = result === test.expected;
      console.log(`${success ? '✅' : '❌'} ${test.desc}: ${result} ${success ? '==' : '!='} ${test.expected}`);
      if (success) passed++;
    });

    console.log(`\n📊 Multi-Tenant Isolation Test: ${passed}/${tests.length} passed\n`);
    return passed === tests.length;

  } catch (error) {
    console.error('❌ Multi-Tenant Isolation Test Failed:', error.message);
    return false;
  }
}

// Test 5: Import Path Verification
async function testImportPaths() {
  console.log('📦 TEST 5: Import Path Verification');
  console.log('=====================================');

  try {
    const fs = require('fs');
    const path = require('path');

    // Check if new auth files exist
    const authFiles = [
      'frontend/src/lib/auth/index.ts',
      'frontend/src/lib/auth/client.ts',
      'frontend/src/lib/auth/session.ts',
      'frontend/src/lib/auth/permissions.ts',
      'frontend/src/lib/auth/jwt.ts',
      'frontend/src/lib/auth/types.ts'
    ];

    console.log('📁 Checking new auth file structure...');
    let filesExist = 0;
    authFiles.forEach(file => {
      const exists = fs.existsSync(file);
      console.log(`${exists ? '✅' : '❌'} ${file}`);
      if (exists) filesExist++;
    });

    // Check if old auth files are removed
    const oldAuthFiles = [
      'frontend/src/lib/auth-helpers.ts',
      'frontend/src/lib/auth/auth-helper.ts',
      'frontend/src/lib/auth/jwt-helper.ts',
      'src/lib/auth-helpers.ts'
    ];

    console.log('\n🗑️  Checking old auth files are removed...');
    let filesRemoved = 0;
    oldAuthFiles.forEach(file => {
      const exists = fs.existsSync(file);
      console.log(`${!exists ? '✅' : '❌'} ${file} ${!exists ? '(removed)' : '(still exists)'}`);
      if (!exists) filesRemoved++;
    });

    const newFilesScore = filesExist / authFiles.length;
    const oldFilesScore = filesRemoved / oldAuthFiles.length;
    const totalScore = (newFilesScore + oldFilesScore) / 2;

    console.log(`\n📊 Import Path Test: ${Math.round(totalScore * 100)}% passed`);
    console.log(`   New files: ${filesExist}/${authFiles.length}`);
    console.log(`   Old files removed: ${filesRemoved}/${oldAuthFiles.length}\n`);

    return totalScore >= 0.9; // 90% threshold

  } catch (error) {
    console.error('❌ Import Path Test Failed:', error.message);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Authentication Tests...\n');

  const tests = [
    { name: 'JWT Claims Structure', fn: testJWTClaimsStructure },
    { name: 'Supabase Client Creation', fn: testSupabaseClientCreation },
    { name: 'Auth Function Compatibility', fn: testAuthFunctionCompatibility },
    { name: 'Multi-Tenant Isolation', fn: testMultiTenantIsolation },
    { name: 'Import Path Verification', fn: testImportPaths }
  ];

  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.error(`❌ ${test.name} failed with error:`, error.message);
      results.push({ name: test.name, passed: false });
    }
  }

  // Summary
  console.log('📋 FINAL TEST SUMMARY');
  console.log('=====================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });

  console.log(`\n🎯 OVERALL SCORE: ${passed}/${total} (${Math.round(passed/total * 100)}%)`);
  
  if (passed === total) {
    console.log('🎉 ALL TESTS PASSED! Authentication refactor is solid.');
  } else if (passed >= total * 0.8) {
    console.log('⚠️  Most tests passed, but some issues need attention.');
  } else {
    console.log('🚨 Multiple test failures detected. Review required.');
  }

  return passed / total;
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().then(score => {
    process.exit(score >= 0.8 ? 0 : 1);
  });
}

module.exports = { runAllTests };
