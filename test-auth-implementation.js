#!/usr/bin/env node

/**
 * Test the actual auth implementation files
 * Verify our new auth structure works correctly
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 TESTING ACTUAL AUTH IMPLEMENTATION\n');

// Test 1: Verify TypeScript compilation of auth files
async function testTypeScriptCompilation() {
  console.log('📝 TEST 1: TypeScript Compilation');
  console.log('==================================');

  try {
    const { spawn } = require('child_process');
    
    return new Promise((resolve) => {
      const tsc = spawn('npx', ['tsc', '--noEmit', '--project', 'frontend/tsconfig.json'], {
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      tsc.stdout.on('data', (data) => {
        output += data.toString();
      });

      tsc.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      tsc.on('close', (code) => {
        if (code === 0) {
          console.log('✅ TypeScript compilation successful');
          console.log('✅ All auth types resolve correctly');
          resolve(true);
        } else {
          console.log('❌ TypeScript compilation failed');
          console.log('Error output:', errorOutput);
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.error('❌ TypeScript test failed:', error.message);
    return false;
  }
}

// Test 2: Verify auth file exports
async function testAuthFileExports() {
  console.log('\n📦 TEST 2: Auth File Exports');
  console.log('=============================');

  try {
    // Read the main auth index file
    const indexPath = 'frontend/src/lib/auth/index.ts';
    const indexContent = fs.readFileSync(indexPath, 'utf8');

    console.log('📄 Checking main auth exports...');

    // Check for key exports
    const expectedExports = [
      'createClient',
      'createServiceClient',
      'withAuth',
      'getServerSession',
      'hasRole',
      'isSuperAdmin',
      'verifyJwt',
      'UserRole',
      'AuthUser',
      'TenantClaims'
    ];

    let exportsFound = 0;
    expectedExports.forEach(exportName => {
      const hasExport = indexContent.includes(exportName);
      console.log(`${hasExport ? '✅' : '❌'} ${exportName}`);
      if (hasExport) exportsFound++;
    });

    console.log(`\n📊 Exports Test: ${exportsFound}/${expectedExports.length} found`);

    // Check individual module files
    const moduleFiles = [
      'frontend/src/lib/auth/client.ts',
      'frontend/src/lib/auth/session.ts',
      'frontend/src/lib/auth/permissions.ts',
      'frontend/src/lib/auth/jwt.ts',
      'frontend/src/lib/auth/types.ts'
    ];

    console.log('\n📁 Checking individual module files...');
    let modulesValid = 0;
    moduleFiles.forEach(file => {
      const exists = fs.existsSync(file);
      if (exists) {
        const content = fs.readFileSync(file, 'utf8');
        const hasExports = content.includes('export');
        console.log(`${hasExports ? '✅' : '❌'} ${path.basename(file)} - ${hasExports ? 'has exports' : 'no exports'}`);
        if (hasExports) modulesValid++;
      } else {
        console.log(`❌ ${path.basename(file)} - file missing`);
      }
    });

    console.log(`\n📊 Module Files: ${modulesValid}/${moduleFiles.length} valid`);

    return exportsFound >= expectedExports.length * 0.8 && modulesValid >= moduleFiles.length * 0.8;

  } catch (error) {
    console.error('❌ Auth exports test failed:', error.message);
    return false;
  }
}

// Test 3: Check for import conflicts
async function testImportConflicts() {
  console.log('\n🔍 TEST 3: Import Conflict Detection');
  console.log('====================================');

  try {
    const { execSync } = require('child_process');

    // Search for old import patterns
    const oldImportPatterns = [
      '@/lib/auth-helpers',
      '@/lib/auth/auth-helper',
      '@/lib/auth/jwt-helper'
    ];

    console.log('🔎 Searching for old import patterns...');
    
    let conflictsFound = 0;
    for (const pattern of oldImportPatterns) {
      try {
        const result = execSync(`grep -r "${pattern}" frontend/src --include="*.ts" --include="*.tsx" || true`, { encoding: 'utf8' });
        if (result.trim()) {
          console.log(`❌ Found old import: ${pattern}`);
          console.log(`   Files: ${result.split('\n').length - 1}`);
          conflictsFound++;
        } else {
          console.log(`✅ No conflicts for: ${pattern}`);
        }
      } catch (error) {
        console.log(`✅ No conflicts for: ${pattern}`);
      }
    }

    // Check for new import usage
    console.log('\n✨ Checking new import usage...');
    try {
      const newImportResult = execSync(`grep -r "from '@/lib/auth'" frontend/src --include="*.ts" --include="*.tsx" | wc -l || echo 0`, { encoding: 'utf8' });
      const newImportCount = parseInt(newImportResult.trim());
      console.log(`✅ Files using new imports: ${newImportCount}`);
    } catch (error) {
      console.log('⚠️  Could not count new imports');
    }

    console.log(`\n📊 Import Conflicts: ${conflictsFound === 0 ? 'NONE' : conflictsFound + ' found'}`);

    return conflictsFound === 0;

  } catch (error) {
    console.error('❌ Import conflict test failed:', error.message);
    return false;
  }
}

// Test 4: Verify ESLint rules
async function testESLintRules() {
  console.log('\n🔧 TEST 4: ESLint Rules Verification');
  console.log('====================================');

  try {
    const eslintConfigPath = 'frontend/.eslintrc.json';
    
    if (!fs.existsSync(eslintConfigPath)) {
      console.log('❌ ESLint config file not found');
      return false;
    }

    const eslintConfig = JSON.parse(fs.readFileSync(eslintConfigPath, 'utf8'));
    
    console.log('📋 Checking ESLint rules...');
    
    // Check for no-restricted-imports rule
    const hasRestrictedImports = eslintConfig.rules && eslintConfig.rules['no-restricted-imports'];
    console.log(`${hasRestrictedImports ? '✅' : '❌'} no-restricted-imports rule configured`);

    if (hasRestrictedImports) {
      const restrictedPaths = eslintConfig.rules['no-restricted-imports'][1]?.paths || [];
      const authHelperRestrictions = restrictedPaths.filter(path => 
        path.name && path.name.includes('auth-helper')
      );
      
      console.log(`✅ Auth helper restrictions: ${authHelperRestrictions.length} configured`);
      authHelperRestrictions.forEach(restriction => {
        console.log(`   - ${restriction.name}: ${restriction.message}`);
      });
    }

    return hasRestrictedImports;

  } catch (error) {
    console.error('❌ ESLint rules test failed:', error.message);
    return false;
  }
}

// Test 5: Function signature compatibility
async function testFunctionSignatures() {
  console.log('\n🔧 TEST 5: Function Signature Compatibility');
  console.log('===========================================');

  try {
    // Read auth files and check function signatures
    const authFiles = {
      'session.ts': 'frontend/src/lib/auth/session.ts',
      'permissions.ts': 'frontend/src/lib/auth/permissions.ts',
      'jwt.ts': 'frontend/src/lib/auth/jwt.ts'
    };

    console.log('🔍 Checking function signatures...');

    const expectedFunctions = {
      'session.ts': ['withAuth', 'getServerSession', 'requireAuth'],
      'permissions.ts': ['hasRole', 'isSuperAdmin', 'hasRoleLegacy'],
      'jwt.ts': ['verifyJwt', 'debugJwtClaims', 'parseClaims']
    };

    let functionsFound = 0;
    let totalExpected = 0;

    Object.entries(expectedFunctions).forEach(([file, functions]) => {
      const filePath = authFiles[file];
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        console.log(`\n📄 ${file}:`);
        functions.forEach(func => {
          totalExpected++;
          const hasFunction = content.includes(`export function ${func}`) || 
                            content.includes(`export async function ${func}`) ||
                            content.includes(`function ${func}`);
          console.log(`  ${hasFunction ? '✅' : '❌'} ${func}`);
          if (hasFunction) functionsFound++;
        });
      } else {
        console.log(`❌ ${file} not found`);
      }
    });

    console.log(`\n📊 Function Signatures: ${functionsFound}/${totalExpected} found`);

    return functionsFound >= totalExpected * 0.9;

  } catch (error) {
    console.error('❌ Function signature test failed:', error.message);
    return false;
  }
}

// Main test runner
async function runImplementationTests() {
  console.log('🚀 Starting Auth Implementation Tests...\n');

  const tests = [
    { name: 'TypeScript Compilation', fn: testTypeScriptCompilation },
    { name: 'Auth File Exports', fn: testAuthFileExports },
    { name: 'Import Conflict Detection', fn: testImportConflicts },
    { name: 'ESLint Rules Verification', fn: testESLintRules },
    { name: 'Function Signature Compatibility', fn: testFunctionSignatures }
  ];

  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.error(`❌ ${test.name} failed with error:`, error.message);
      results.push({ name: test.name, passed: false });
    }
  }

  // Summary
  console.log('\n📋 IMPLEMENTATION TEST SUMMARY');
  console.log('==============================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });

  console.log(`\n🎯 IMPLEMENTATION SCORE: ${passed}/${total} (${Math.round(passed/total * 100)}%)`);
  
  if (passed === total) {
    console.log('🎉 ALL IMPLEMENTATION TESTS PASSED!');
  } else if (passed >= total * 0.8) {
    console.log('⚠️  Most implementation tests passed.');
  } else {
    console.log('🚨 Multiple implementation failures detected.');
  }

  return passed / total;
}

// Run tests if called directly
if (require.main === module) {
  runImplementationTests().then(score => {
    process.exit(score >= 0.8 ? 0 : 1);
  });
}

module.exports = { runImplementationTests };
