#!/bin/bash

# Fix UserRole import issues
echo "🔧 Fixing UserRole import issues..."

# Files that need UserRole imported
FILES_NEEDING_USERROLE=(
  "frontend/src/app/api/admin/prompts/route.ts"
  "frontend/src/app/api/documents/__tests__/upload-route.test.ts"
  "frontend/src/app/api/graph/case/[caseId]/network/route.ts"
  "frontend/src/app/api/graph/person/[personId]/network/route.ts"
  "frontend/src/app/api/graph/staff/[staffId]/cases/route.ts"
  "frontend/src/lib/middlewares/__tests__/rate-limit-middleware.test.ts"
)

for file in "${FILES_NEEDING_USERROLE[@]}"; do
  if [ -f "$file" ]; then
    echo "📝 Fixing UserRole imports in $file"
    
    # Check if the file already imports from @/lib/auth
    if grep -q "from '@/lib/auth'" "$file"; then
      # Add UserRole to existing import
      sed -i '' 's/from '\''@\/lib\/auth'\''/&/g' "$file"
      sed -i '' 's/import { \([^}]*\) } from '\''@\/lib\/auth'\''/import { \1, UserRole } from '\''@\/lib\/auth'\''/g' "$file"
      # Remove duplicate UserRole if it exists
      sed -i '' 's/, UserRole, UserRole/, UserRole/g' "$file"
    else
      # Add new import line
      # Find the last import line and add after it
      sed -i '' '/^import.*from/a\
import { UserRole } from '\''@/lib/auth'\'';
' "$file"
    fi
  else
    echo "⚠️  File not found: $file"
  fi
done

echo "✅ UserRole import fixes complete!"
