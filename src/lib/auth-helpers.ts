import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { Database } from '@/types/supabase'

// Types for auth functions
interface AuthUser {
  id: string;
  email?: string;
  user_metadata?: {
    role?: string;
    [key: string]: any;
  };
  app_metadata?: {
    roles?: string[];
    [key: string]: any;
  };
}

// Constants
const COOKIE_NAME = 'sb-auth-token'
const REFRESH_MARGIN = 60 * 1000 // 1 minute in ms

// Environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''

/**
 * Create a Supabase client for server-side use
 * 
 * @returns Supabase client
 */
export const createClient = () => {
  const cookieStore = cookies()
  
  return createServerClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set({ name, value: '', ...options })
        },
      },
    }
  )
}

/**
 * Create a Supabase client for middleware use
 * 
 * @param request - Next.js request
 * @returns Supabase client and response
 */
export function createMiddlewareClient(request: NextRequest) {
  // Create an unmodified response
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          // Update the response cookies
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: CookieOptions) {
          // Update the response cookies
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  return { supabase, response }
}

/**
 * Create a Supabase client for API route use
 * 
 * @param req - Next.js request
 * @param res - Next.js response
 * @returns Supabase client
 */
export function createRouteHandlerClient(req: NextRequest) {
  const cookieStore = cookies()
  
  return createServerClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set({ name, value: '', ...options })
        },
      },
    }
  )
}

/**
 * Middleware to handle authentication
 * 
 * @param req - Next.js request
 * @returns Next.js response
 */
export async function authMiddleware(req: NextRequest) {
  // Create a Supabase client
  const { supabase, response } = createMiddlewareClient(req)

  // Refresh session if needed
  await refreshSession(supabase)

  // Get the current session
  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Get the URL pathname
  const path = req.nextUrl.pathname

  // Check if the user is authenticated
  const isAuthenticated = !!session
  const isAuthRoute = path.startsWith('/auth')
  const isPublicRoute = path === '/' || path.startsWith('/public') || path.startsWith('/api/public')
  const isAdminRoute = path.startsWith('/admin')
  const isClientRoute = path.startsWith('/client')

  // Handle authentication redirects
  if (isAuthRoute) {
    // If the user is authenticated and trying to access auth routes, redirect to dashboard
    if (isAuthenticated) {
      const redirectUrl = new URL('/dashboard', req.url)
      return NextResponse.redirect(redirectUrl)
    }
    // Otherwise, allow access to auth routes
    return response
  }

  // Handle admin routes
  if (isAdminRoute) {
    // Check if the user is an admin
    const isAdmin = session?.user?.user_metadata?.role === 'admin'

    // If the user is not authenticated or not an admin, redirect to login
    if (!isAuthenticated || !isAdmin) {
      const redirectUrl = new URL('/auth/login', req.url)
      redirectUrl.searchParams.set('redirect', path)
      return NextResponse.redirect(redirectUrl)
    }
    // Otherwise, allow access to admin routes
    return response
  }

  // Handle client routes
  if (isClientRoute) {
    // Check if the user is a client
    const isClient = session?.user?.user_metadata?.role === 'client'

    // If the user is not authenticated or not a client, redirect to client login
    if (!isAuthenticated || !isClient) {
      const redirectUrl = new URL('/auth/client-login', req.url)
      redirectUrl.searchParams.set('redirect', path)
      return NextResponse.redirect(redirectUrl)
    }
    // Otherwise, allow access to client routes
    return response
  }

  // Handle protected routes
  if (!isAuthenticated && !isPublicRoute) {
    // Redirect to login
    const redirectUrl = new URL('/auth/login', req.url)
    redirectUrl.searchParams.set('redirect', path)
    return NextResponse.redirect(redirectUrl)
  }

  // Allow access to public routes and authenticated routes for authenticated users
  return response
}

/**
 * Refresh the session if it's about to expire
 * 
 * @param supabase - Supabase client
 */
async function refreshSession(supabase: ReturnType<typeof createServerClient>) {
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (session) {
    // Check if the session is about to expire
    const expiresAt = session.expires_at * 1000 // convert to ms
    const now = Date.now()

    if (expiresAt - now < REFRESH_MARGIN) {
      // Refresh the session
      await supabase.auth.refreshSession()
    }
  }
}

/**
 * Higher-order function to protect API routes
 * 
 * @param handler - API route handler
 * @param options - Protection options
 * @returns Protected API route handler
 */
export function withAuth<T>(
  handler: (req: NextRequest, context: any, callback: (user: any, supabase: any) => Promise<T>) => Promise<T>,
  options: {
    roles?: string[]
    redirectTo?: string
  } = {}
) {
  return async (req: NextRequest, context: any) => {
    // Create a Supabase client
    const supabase = createRouteHandlerClient(req)

    // Get the current session
    const {
      data: { session },
    } = await supabase.auth.getSession()

    // Check if the user is authenticated
    if (!session) {
      // Return 401 Unauthorized
      return NextResponse.json(
        { error: 'Unauthorized', message: 'You must be logged in to access this resource' },
        { status: 401 }
      )
    }

    // Check if the user has the required role
    if (options.roles && options.roles.length > 0) {
      const userRole = session.user.user_metadata.role

      if (!userRole || !options.roles.includes(userRole)) {
        // Return 403 Forbidden
        return NextResponse.json(
          { error: 'Forbidden', message: 'You do not have permission to access this resource' },
          { status: 403 }
        )
      }
    }

    // Call the handler with the user and Supabase client
    return await handler(req, context, async (user, supabase) => await callback(user, supabase))
  }
}

// ============================================================================
// AUTH UTILITY FUNCTIONS
// ============================================================================
// These functions provide compatibility with the admin routes

/**
 * Check if a user is authenticated
 * @param user The user object from Supabase session
 * @returns True if the user is authenticated
 */
export function isAuthenticated(user: AuthUser | null): boolean {
  return !!user && !!user.id;
}

/**
 * Check if a user has one of the specified roles
 * @param user The user object from Supabase session
 * @param roles The roles to check against
 * @returns True if the user has one of the roles
 */
export function hasRole(user: AuthUser | null, roles: string[]): boolean {
  if (!user) return false;

  // Check user_metadata.role (legacy pattern)
  if (user.user_metadata?.role && roles.includes(user.user_metadata.role)) {
    return true;
  }

  // Check app_metadata.roles array (Supabase standard)
  if (user.app_metadata?.roles && Array.isArray(user.app_metadata.roles)) {
    return user.app_metadata.roles.some((role: string) => roles.includes(role));
  }

  return false;
}
